# SaaS Comidas Rápidas - FastFood Orders

## 🍔 Descripción
SaaS completo para gestión de pedidos de comidas rápidas con dashboard administrativo, menú personalizable, carrito de compras y integración con Mercado Pago para Latinoamérica.

## 🏗️ Arquitectura Técnica

### Stack Tecnológico
- **Frontend**: Next.js 14 (App Router)
- **Backend**: FastAPI (Python)
- **Base de Datos**: PostgreSQL
- **Administrador DB**: Adminer
- **Autenticación**: Clerk
- **Pagos**: Mercado Pago API
- **Containerización**: Docker & Docker Compose

### Servicios
```
├── frontend/          # Next.js App
├── backend/           # FastAPI
├── database/          # PostgreSQL
├── adminer/          # DB Admin Interface
└── docker-compose.yml
```

## 🎯 Funcionalidades Principales

### Para Clientes
- ✅ Menú interactivo con categorías
- ✅ Personalización de productos
- ✅ Carrito de compras persistente
- ✅ Sistema de promociones
- ✅ Pago con Mercado Pago
- ✅ Dashboard de pedidos
- ✅ Historial de compras

### Para Administradores
- ✅ Dashboard de ventas
- ✅ Gestión de menú y productos
- ✅ Configuración de promociones
- ✅ Reportes y analytics
- ✅ Gestión de pedidos en tiempo real

## 🚀 Instalación Rápida

```bash
# Clonar repositorio
git clone <repo-url>
cd project-pedidos_A4

# Configurar variables de entorno
cp .env.example .env

# Levantar servicios con Docker
docker-compose up -d

# Acceder a la aplicación
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# Adminer: http://localhost:8080
```

## 🔧 Configuración

### Variables de Entorno Requeridas
```env
# Base de datos
POSTGRES_DB=fastfood_db
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# Mercado Pago
MERCADO_PAGO_ACCESS_TOKEN=TEST-...
MERCADO_PAGO_PUBLIC_KEY=TEST-...

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 📊 Modelo de Datos

### Entidades Principales
- **Users**: Usuarios del sistema (clientes/admins)
- **Products**: Productos del menú
- **Categories**: Categorías de productos
- **Orders**: Pedidos realizados
- **OrderItems**: Items individuales de cada pedido
- **Payments**: Transacciones de pago
- **Promotions**: Ofertas y descuentos

## 🛠️ Desarrollo

### Comandos Útiles
```bash
# Desarrollo local
npm run dev          # Next.js
uvicorn main:app --reload  # FastAPI

# Testing
npm run test         # Frontend tests
pytest              # Backend tests

# Linting
npm run lint         # Frontend
flake8 .            # Backend
```

## 🔐 Seguridad
- Autenticación JWT via Clerk
- Validación de datos con Pydantic
- Sanitización de inputs
- Rate limiting en APIs
- HTTPS en producción

## 📱 Responsive Design
- Mobile-first approach
- PWA capabilities
- Optimizado para tablets y móviles

## 🌎 Mercado Pago Integration
- Soporte para múltiples países LATAM
- Pagos con tarjeta y efectivo
- Webhooks para confirmación de pagos
- Manejo de estados de transacción

## 📞 Soporte
Para reportar bugs o solicitar funcionalidades, crear un issue en el repositorio.

---
**Desarrollado con ❤️ para el ecosistema de comidas rápidas**