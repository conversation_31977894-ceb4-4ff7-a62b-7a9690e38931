# 🔄 Git Workflow - SaaS Comidas Rápidas

## 📋 Comandos Esenciales

### **Verificar Estado Actual**
```bash
git branch -a                    # Ver todas las ramas
git status                       # Estado actual
git log --oneline -10           # Últimos 10 commits
```

### **Cambiar entre Ramas**
```bash
# Ir a main (código estable)
git checkout main

# Ir a dev (desarrollo frontend)
git checkout dev

# Ir a configuración Clerk
git checkout feature/configurar-clerk
```

---

## 🎯 Flujo por Rama

### **1. 🔧 Trabajar en Configuración Clerk**
```bash
# Cambiar a la rama de Clerk
git checkout feature/configurar-clerk

# Hacer cambios...
# Agregar archivos
git add .

# Commit con mensaje descriptivo
git commit -m "✅ Configurar ClerkProvider en layout principal"

# Push a la rama
git push origin feature/configurar-clerk
```

### **2. 🚀 Desarrollar Frontend en DEV**
```bash
# Cambiar a dev
git checkout dev

# Mergear configuración Clerk si está lista
git merge feature/configurar-clerk

# Hacer desarrollo frontend...
git add .
git commit -m "🚀 Implementar componentes UI base"

# Push a dev
git push origin dev
```

### **3. 📦 Merge a MAIN (cuando esté listo)**
```bash
# Cambiar a main
git checkout main

# Mergear dev cuando esté estable
git merge dev

# Push a main
git push origin main
```

---

## 📝 Convenciones de Commit

| Emoji | Tipo | Descripción | Ejemplo |
|-------|------|-------------|---------|
| ✅ | Config | Configuración completada | `✅ Configurar Clerk authentication` |
| 🚀 | Feature | Nueva funcionalidad | `🚀 Implementar página de menú` |
| 💄 | UI | Mejoras de interfaz | `💄 Mejorar diseño del carrito` |
| 🐛 | Fix | Corrección de bugs | `🐛 Corregir validación de forms` |
| ♻️ | Refactor | Refactoring de código | `♻️ Reorganizar componentes UI` |
| 🔧 | Tooling | Herramientas/config | `🔧 Configurar ESLint rules` |
| 📝 | Docs | Documentación | `📝 Actualizar README` |
| 🧪 | Test | Testing | `🧪 Agregar tests para auth` |

---

## 🏗️ Estructura de Trabajo por Rama

### **📍 MAIN Branch**
```
Estado: ✅ Estable
Contenido: Backend Phase 2 completo
Propósito: Código listo para producción

Archivos principales:
- backend/app/ (APIs completas)
- database/schema.sql
- docker-compose.yml
- Documentación completa
```

### **📍 DEV Branch**
```
Estado: 🔄 Desarrollo activo
Base: main branch
Propósito: Desarrollo frontend + integración

Trabajo pendiente:
- Frontend Next.js 14
- Componentes UI
- Páginas principales
- Integración con backend
- Testing
```

### **📍 feature/configurar-clerk Branch**
```
Estado: 🔄 Pendiente
Base: dev branch  
Propósito: Solo configuración Clerk

Tareas específicas:
- @clerk/nextjs setup
- Auth components
- Middleware setup
- Role management
- Backend sync
```

---

## 🔄 Ejemplo de Sesión de Trabajo

### **Scenario 1: Configurar Clerk**
```bash
# 1. Ir a la rama de Clerk
git checkout feature/configurar-clerk

# 2. Instalar dependencias
cd frontend && npm install @clerk/nextjs

# 3. Hacer configuración...
# ... editar archivos ...

# 4. Commit cambios
git add .
git commit -m "✅ Instalar y configurar @clerk/nextjs

- Agregar ClerkProvider en layout
- Configurar variables de entorno
- Crear componentes básicos de auth"

# 5. Push cambios
git push origin feature/configurar-clerk
```

### **Scenario 2: Desarrollo Frontend**
```bash
# 1. Ir a dev
git checkout dev

# 2. Mergear Clerk si está listo
git merge feature/configurar-clerk

# 3. Desarrollar features...
# ... crear componentes ...

# 4. Commit
git add .
git commit -m "🚀 Implementar página de menú con filtros

- Crear MenuPage component
- Agregar filtros por categoría
- Implementar búsqueda en tiempo real
- Conectar con API de productos"

# 5. Push
git push origin dev
```

---

## 🎯 Siguiente Paso Recomendado

```bash
# Ir a la rama de configuración Clerk
git checkout feature/configurar-clerk

# Comenzar configuración de autenticación
echo "🎯 Listo para configurar Clerk authentication!"
```

**Orden sugerido de trabajo:**
1. **feature/configurar-clerk** → Setup auth completo
2. **dev** → Merge clerk + desarrollo frontend  
3. **main** → Merge final cuando todo esté estable