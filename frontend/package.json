{"name": "fastfood-frontend", "version": "1.0.0", "description": "SaaS Comidas <PERSON> - Frontend Next.js", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out", "postbuild": "next-sitemap", "prepare": "husky install"}, "dependencies": {"@clerk/nextjs": "^4.27.1", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-badge": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-sheet": "^1.0.4", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.294.0", "next": "14.0.3", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "sonner": "^1.2.4", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.7.9", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tailwindcss": "^3.13.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.1.0", "next-sitemap": "^4.2.3", "postcss": "^8.4.31", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["nextjs", "react", "typescript", "tailwindcss", "saas", "comidas", "fastfood", "mercadopago", "clerk"], "author": "FastFood Team", "license": "MIT"}