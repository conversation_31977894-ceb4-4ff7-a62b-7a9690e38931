# =====================================================
# Frontend Next.js - Multi-stage Dockerfile
# =====================================================

FROM node:18-alpine AS base

# Instalar dependencias del sistema necesarias
RUN apk add --no-cache libc6-compat curl

# Crear usuario no-root
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

WORKDIR /app

# =====================================================
# Stage: Dependencias
# =====================================================
FROM base AS dependencies

# Copiar archivos de package
COPY package.json package-lock.json* ./

# Instalar dependencias
RUN npm ci --omit=dev && npm cache clean --force

# =====================================================
# Stage: Builder 
# =====================================================
FROM base AS builder

WORKDIR /app

# Copiar dependencias desde la etapa anterior
COPY --from=dependencies /app/node_modules ./node_modules

# Copiar código fuente
COPY . .

# Variables de entorno para build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# Construir la aplicación
RUN npm run build

# =====================================================
# Stage: Desarrollo
# =====================================================
FROM base AS development

WORKDIR /app

# Copiar package files
COPY package.json package-lock.json* ./

# Instalar todas las dependencias (incluyendo dev)
RUN npm ci && npm cache clean --force

# Copiar código fuente
COPY . .

# Cambiar ownership
RUN chown -R nextjs:nodejs /app

# Cambiar a usuario no-root
USER nextjs

# Puerto de desarrollo
EXPOSE 3000

# Variables de entorno
ENV NODE_ENV development
ENV NEXT_TELEMETRY_DISABLED 1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Comando de desarrollo
CMD ["npm", "run", "dev"]

# =====================================================
# Stage: Producción
# =====================================================
FROM base AS production

WORKDIR /app

# Variables de entorno
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# Copiar archivos públicos
COPY --from=builder /app/public ./public

# Copiar build output
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Cambiar a usuario no-root
USER nextjs

# Puerto de producción
EXPOSE 3000

# Health check  
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Comando de producción
CMD ["node", "server.js"]