import { useUser, useAuth as useClerkAuth } from '@clerk/nextjs'
import { useEffect, useState } from 'react'

export interface User {
  id: string
  email: string
  firstName: string | null
  lastName: string | null
  role: 'customer' | 'admin' | 'staff'
  isActive: boolean
}

export function useAuth() {
  const { user, isLoaded, isSignedIn } = useUser()
  const { getToken, signOut } = useClerkAuth()
  const [backendUser, setBackendUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Función para obtener o crear usuario en backend
  const syncUserWithBackend = async () => {
    if (!user || !isSignedIn) {
      setBackendUser(null)
      setIsLoading(false)
      return
    }

    try {
      const token = await getToken()
      
      // Primero intentar obtener el usuario existente
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const userData = await response.json()
        setBackendUser(userData)
      } else if (response.status === 404) {
        // Usuario no existe en backend, crear uno nuevo
        const createResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            clerk_id: user.id,
            email: user.emailAddresses[0]?.emailAddress || '',
            first_name: user.firstName || '',
            last_name: user.lastName || '',
            role: 'customer'
          })
        })

        if (createResponse.ok) {
          const newUser = await createResponse.json()
          setBackendUser(newUser)
        } else {
          console.error('Error creating user in backend')
        }
      } else {
        console.error('Error fetching user from backend')
      }
    } catch (error) {
      console.error('Error syncing user with backend:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (isLoaded) {
      syncUserWithBackend()
    }
  }, [isLoaded, isSignedIn, user?.id])

  const logout = async () => {
    await signOut()
    setBackendUser(null)
  }

  const hasRole = (role: string | string[]) => {
    if (!backendUser) return false
    const roles = Array.isArray(role) ? role : [role]
    return roles.includes(backendUser.role)
  }

  const isAdmin = () => hasRole(['admin'])
  const isStaff = () => hasRole(['admin', 'staff'])
  const isCustomer = () => hasRole(['customer', 'admin', 'staff'])

  return {
    // Estados
    isLoaded,
    isSignedIn,
    isLoading,
    
    // Datos del usuario
    clerkUser: user,
    user: backendUser,
    
    // Funciones
    getToken,
    logout,
    syncUserWithBackend,
    
    // Helpers de roles
    hasRole,
    isAdmin,
    isStaff,
    isCustomer,
  }
}