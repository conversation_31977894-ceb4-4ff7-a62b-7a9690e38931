'use client'

import Link from 'next/link'
import { User<PERSON>utton, useUser } from '@clerk/nextjs'
import { ShoppingCart, Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

export function Header() {
  const { isSignedIn, user } = useUser()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const navigation = [
    { name: 'Inicio', href: '/' },
    { name: '<PERSON><PERSON>', href: '/menu' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/about' },
    { name: '<PERSON><PERSON>', href: '/contact' },
  ]

  const authNavigation = isSignedIn
    ? [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'Mis Pedidos', href: '/orders' },
      ]
    : []

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
        <div className="flex w-full items-center justify-between py-6">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <span className="text-2xl font-bold text-orange-600">FastFood</span>
            </Link>
          </div>

          {/* Desktop navigation */}
          <div className="ml-10 hidden space-x-8 lg:block">
            {navigation.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className="text-base font-medium text-gray-500 hover:text-orange-600 transition-colors"
              >
                {link.name}
              </Link>
            ))}
            {authNavigation.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className="text-base font-medium text-gray-500 hover:text-orange-600 transition-colors"
              >
                {link.name}
              </Link>
            ))}
          </div>

          {/* Desktop actions */}
          <div className="ml-10 hidden space-x-4 lg:flex lg:items-center">
            <Button variant="ghost" size="icon" className="relative">
              <ShoppingCart className="h-5 w-5" />
              <span className="absolute -top-2 -right-2 h-4 w-4 bg-orange-600 text-white text-xs rounded-full flex items-center justify-center">
                0
              </span>
            </Button>

            {isSignedIn ? (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-500">
                  Hola, {user?.firstName || 'Usuario'}
                </span>
                <UserButton
                  appearance={{
                    elements: {
                      avatarBox: 'w-8 h-8',
                    },
                  }}
                />
              </div>
            ) : (
              <div className="flex space-x-3">
                <Button variant="ghost" asChild>
                  <Link href="/sign-in">Iniciar Sesión</Link>
                </Button>
                <Button asChild>
                  <Link href="/sign-up">Registrarse</Link>
                </Button>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center lg:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="h-6 w-6" aria-hidden="true" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {navigation.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="block rounded-md px-3 py-2 text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-orange-600"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {link.name}
                </Link>
              ))}
              {authNavigation.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="block rounded-md px-3 py-2 text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-orange-600"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {link.name}
                </Link>
              ))}
            </div>
            <div className="border-t border-gray-200 pb-3 pt-4">
              <div className="flex items-center px-4 space-x-3">
                {isSignedIn ? (
                  <>
                    <div className="flex-shrink-0">
                      <UserButton />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {user?.firstName} {user?.lastName}
                      </p>
                      <p className="text-sm text-gray-500">
                        {user?.emailAddresses[0]?.emailAddress}
                      </p>
                    </div>
                  </>
                ) : (
                  <div className="flex space-x-3 w-full">
                    <Button variant="ghost" className="flex-1" asChild>
                      <Link href="/sign-in" onClick={() => setMobileMenuOpen(false)}>
                        Iniciar Sesión
                      </Link>
                    </Button>
                    <Button className="flex-1" asChild>
                      <Link href="/sign-up" onClick={() => setMobileMenuOpen(false)}>
                        Registrarse
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}