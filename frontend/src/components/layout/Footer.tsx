import Link from 'next/link'
import { Facebook, Instagram, Twitter, MapPin, Phone, Mail } from 'lucide-react'

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <div className="flex items-center">
                <span className="text-2xl font-bold text-orange-500">FastFood</span>
              </div>
              <p className="text-gray-300 text-sm">
                La mejor comida rápida de la ciudad, con ingredientes frescos y 
                servicio de calidad. Pedí online y disfrutá en casa.
              </p>
              <div className="flex space-x-4">
                <Link href="#" className="text-gray-400 hover:text-orange-500 transition-colors">
                  <Facebook className="h-5 w-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-orange-500 transition-colors">
                  <Instagram className="h-5 w-5" />
                </Link>
                <Link href="#" className="text-gray-400 hover:text-orange-500 transition-colors">
                  <Twitter className="h-5 w-5" />
                </Link>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Enlaces Rápidos</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/menu" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Ver Menú
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Nosotros
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Contacto
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Términos y Condiciones
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Política de Privacidad
                  </Link>
                </li>
              </ul>
            </div>

            {/* Customer Service */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Atención al Cliente</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/help" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Centro de Ayuda
                  </Link>
                </li>
                <li>
                  <Link href="/orders" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Seguir Pedido
                  </Link>
                </li>
                <li>
                  <Link href="/returns" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Devoluciones
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="text-gray-300 hover:text-orange-500 transition-colors text-sm">
                    Preguntas Frecuentes
                  </Link>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Contacto</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-orange-500 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">
                    Av. Principal 123, Ciudad, CP 1234
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-orange-500 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">+54 11 1234-5678</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-orange-500 flex-shrink-0" />
                  <span className="text-gray-300 text-sm"><EMAIL></span>
                </div>
              </div>
              <div className="text-sm text-gray-300">
                <p className="font-medium">Horarios de Atención:</p>
                <p>Lun - Dom: 10:00 - 23:00</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-gray-800 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 FastFood SaaS. Todos los derechos reservados.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/terms" className="text-gray-400 hover:text-orange-500 transition-colors text-sm">
                Términos
              </Link>
              <Link href="/privacy" className="text-gray-400 hover:text-orange-500 transition-colors text-sm">
                Privacidad
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-orange-500 transition-colors text-sm">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}