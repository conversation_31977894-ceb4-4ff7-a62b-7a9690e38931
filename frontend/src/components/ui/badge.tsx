import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-orange-600 text-orange-50 hover:bg-orange-700",
        secondary: "border-transparent bg-orange-100 text-orange-900 hover:bg-orange-200",
        destructive: "border-transparent bg-red-500 text-red-50 hover:bg-red-600",
        outline: "text-orange-600 border-orange-200",
        success: "border-transparent bg-green-600 text-green-50 hover:bg-green-700",
        warning: "border-transparent bg-yellow-600 text-yellow-50 hover:bg-yellow-700",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }