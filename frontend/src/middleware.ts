import { authMiddleware } from '@clerk/nextjs'

export default authMiddleware({
  // Array de rutas públicas (no requieren autenticación)
  publicRoutes: [
    '/',
    '/menu',
    '/about',
    '/contact',
    '/api/health',
    '/sign-in(.*)',
    '/sign-up(.*)',
  ],
  
  // Array de rutas ignoradas (no pasan por middleware)
  ignoredRoutes: [
    '/api/webhook(.*)',
    '/_next(.*)',
    '/favicon.ico',
    '/manifest.json',
  ],
  
  // Configuración de redirects
  afterAuth(auth, req) {
    // Si el usuario está autenticado y está en una página de auth, redirigir al dashboard
    if (auth.userId && (req.nextUrl.pathname === '/sign-in' || req.nextUrl.pathname === '/sign-up')) {
      return Response.redirect(new URL('/dashboard', req.url))
    }
    
    // Si el usuario no está autenticado y está intentando acceder a rutas protegidas
    if (!auth.userId && !auth.isPublicRoute) {
      return Response.redirect(new URL('/sign-in', req.url))
    }
    
    // Si está todo bien, continuar
    return null
  }
})

export const config = {
  matcher: [
    // Hacer que el middleware corra en todas las rutas excepto:
    // - archivos estáticos
    // - archivos internos de Next.js
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Incluir siempre las rutas de API
    '/(api|trpc)(.*)',
  ]
}