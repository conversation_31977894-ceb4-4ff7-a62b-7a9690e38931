import Link from 'next/link'
import { ArrowR<PERSON>, Clock, Shield, Truck } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-orange-600 to-orange-700 text-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl font-extrabold tracking-tight sm:text-5xl md:text-6xl">
              La mejor comida rápida
              <span className="block text-orange-200">a un click de distancia</span>
            </h1>
            <p className="mx-auto mt-6 max-w-2xl text-xl text-orange-100">
              Des<PERSON><PERSON><PERSON><PERSON> sabores ú<PERSON>, ingredientes frescos y entrega súper rápida. 
              Hacé tu pedido online y disfrutá en casa.
            </p>
            <div className="mt-10 flex justify-center space-x-4">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/menu">
                  Ver Menú
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-orange-600" asChild>
                <Link href="/sign-up">Crear Cuenta</Link>
              </Button>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-black opacity-20"></div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
              ¿Por qué elegirnos?
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Experiencia única en comida rápida con calidad premium
            </p>
          </div>

          <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-md bg-orange-500 text-white">
                  <Clock className="h-6 w-6" />
                </div>
                <CardTitle>Entrega Rápida</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Entrega en 30 minutos o menos. Tu comida llegará caliente y fresca directo a tu puerta.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-md bg-orange-500 text-white">
                  <Shield className="h-6 w-6" />
                </div>
                <CardTitle>Calidad Garantizada</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Ingredientes frescos y de primera calidad. Preparamos cada pedido con dedicación y cuidado.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-md bg-orange-500 text-white">
                  <Truck className="h-6 w-6" />
                </div>
                <CardTitle>Seguimiento en Tiempo Real</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Seguí tu pedido desde la cocina hasta tu puerta. Sabé exactamente cuándo llegará tu comida.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-orange-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              ¿Tenés hambre?
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Explorá nuestro menú y hacé tu primer pedido hoy
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/menu">
                  Explorar Menú
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}