import { Suspense } from 'react'
import { Search, Filter } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function MenuPage() {
  // Mock data - será reemplazado por datos reales del backend
  const categories = [
    { id: 1, name: 'Hamburguesas', count: 12 },
    { id: 2, name: 'Pizzas', count: 8 },
    { id: 3, name: '<PERSON>bid<PERSON>', count: 15 },
    { id: 4, name: '<PERSON><PERSON>', count: 6 },
  ]

  const featuredProducts = [
    {
      id: 1,
      name: 'Hamburguesa Clásica',
      description: 'Carne 100% vacuna, lechuga, tomate, cebolla y nuestra salsa especial',
      price: 1299,
      image: '/placeholder-burger.jpg',
      category: 'Hamburguesas',
      featured: true
    },
    {
      id: 2,
      name: 'Pizza Margherita',
      description: 'Salsa de tomate, mozzarella fresca, albahaca y aceite de oliva',
      price: 1599,
      image: '/placeholder-pizza.jpg',
      category: 'Pizzas',
      featured: true
    },
    {
      id: 3,
      name: 'Combo Familiar',
      description: '2 hamburguesas + papas grandes + 2 bebidas',
      price: 2999,
      image: '/placeholder-combo.jpg',
      category: 'Combos',
      featured: true
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white border-b">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
              Nuestro Menú
            </h1>
            <p className="mt-4 text-xl text-gray-600">
              Descubrí todos nuestros productos y encontrá tu favorito
            </p>
          </div>

          {/* Search and Filter */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar productos..."
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="sm:w-auto">
              <Filter className="h-4 w-4 mr-2" />
              Filtros
            </Button>
          </div>
        </div>
      </section>

      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Categories */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Categorías</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="ghost" className="w-full justify-start">
                  Todos los productos
                </Button>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant="ghost"
                    className="w-full justify-between"
                  >
                    {category.name}
                    <Badge variant="secondary">{category.count}</Badge>
                  </Button>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Featured Products */}
            <section className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Productos Destacados</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {featuredProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="aspect-video bg-gray-200 relative">
                      {/* Placeholder for product image */}
                      <div className="w-full h-full bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center">
                        <span className="text-orange-600 font-medium">Imagen del producto</span>
                      </div>
                      {product.featured && (
                        <Badge className="absolute top-2 left-2">Destacado</Badge>
                      )}
                    </div>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{product.name}</CardTitle>
                        <span className="text-xl font-bold text-orange-600">
                          ${(product.price / 100).toFixed(2)}
                        </span>
                      </div>
                      <CardDescription>{product.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full">
                        Agregar al carrito
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>

            {/* All Products */}
            <section>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Todos los Productos</h2>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">Precio: Menor a Mayor</Button>
                </div>
              </div>
              
              <div className="text-center py-12">
                <p className="text-gray-500">
                  Los productos se cargarán desde el backend...
                </p>
                <Button className="mt-4" disabled>
                  Cargando productos...
                </Button>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}