# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **SaaS FastFood Orders** application built for the Latin American market, currently **82% complete** in terms of foundational setup. It's a comprehensive food ordering platform with administrative dashboard, customizable menu, shopping cart, and Mercado Pago payment integration.

## Architecture

**Multi-service containerized application** with clear separation of concerns:
- **Frontend**: Next.js 14 (App Router) + TypeScript + Tailwind CSS
- **Backend**: FastAPI + SQLAlchemy + PostgreSQL  
- **Authentication**: Clerk (JWT-based)
- **Payments**: Mercado Pago (Latin America focused)
- **Infrastructure**: Docker Compose with PostgreSQL, Adminer, Redis

## Development Commands

### Quick Start
```bash
# Complete project startup (recommended)
./start.sh

# Manual Docker commands
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d
```

### Frontend Development (Next.js)
```bash
cd frontend/
npm run dev          # Development server
npm run build        # Production build
npm run lint         # ESLint check
npm run lint:fix     # Auto-fix ESLint issues
npm run type-check   # TypeScript check
npm run test         # Jest tests
npm run test:watch   # Jest in watch mode
npm run test:coverage # Jest with coverage report
npm run format       # Prettier formatting
npm run format:check # Check formatting without fixing
npm run analyze      # Bundle analyzer
npm run clean        # Remove build artifacts
```

### Backend Development (FastAPI)
```bash
cd backend/
# Development (in container)
docker-compose exec backend uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Testing (when implemented)
pytest                    # Run all tests
pytest tests/test_api.py  # Run specific test file
pytest -k "test_name"     # Run specific test by name
pytest --cov=app         # Run with coverage

# Code formatting and linting
black . && isort .       # Format code
flake8 .                 # Linting
mypy .                   # Type checking
```

### Database Management
```bash
# Access database directly
docker-compose exec database psql -U admin -d fastfood_db

# View logs
docker-compose logs -f database

# Database admin interface: http://localhost:8080
# Credentials: PostgreSQL, server: database, user: admin, password: (see .env)
```

## Service URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database Admin**: http://localhost:8080 (Adminer)
- **Redis**: localhost:6379

## Code Architecture Patterns

### Backend Structure (FastAPI)
```
backend/app/
   api/v1/          # API endpoints by domain (products/, orders/, auth/)
   core/            # Configuration, database, security
   models/          # SQLAlchemy ORM models
   schemas/         # Pydantic request/response schemas
   services/        # Business logic layer
```

**Key Patterns**:
- **Dependency Injection**: Database sessions, authentication
- **Pydantic Validation**: All input/output data validation
- **Repository Pattern**: Data access abstraction (planned)
- **Service Layer**: Business logic separation from endpoints
- **JWT Authentication**: Via Clerk integration with role-based authorization

### Frontend Structure (Next.js 14)
```
frontend/src/
   app/             # App Router pages and layouts
      (auth)/      # Authentication layout group
      dashboard/   # Admin dashboard routes
      menu/        # Customer menu routes
   components/      # Reusable React components
      ui/          # Base UI components (buttons, cards, etc.)
      forms/       # Form-specific components
   lib/             # Utilities, API client, configurations
   types/           # TypeScript type definitions
```

**Key Patterns**:
- **App Router**: File-based routing with Next.js 14
- **Server Components**: React Server Components for performance
- **Client Components**: Interactive UI with 'use client'
- **State Management**: Zustand for global state (planned)
- **Form Handling**: React Hook Form + Zod validation
- **API Integration**: React Query for server state management

## Database Schema

Comprehensive PostgreSQL schema with these core entities:
- **users**: Customer/admin accounts (Clerk integration)
- **categories**: Product categories  
- **products**: Menu items with customization options (JSONB)
- **orders**: Order management with status tracking
- **order_items**: Individual line items with customizations
- **payments**: Mercado Pago payment tracking
- **promotions**: Discount system
- **delivery_addresses**: Customer locations
- **order_status_history**: Audit trail
- **user_favorites**: Customer preferences

**Notable Features**:
- UUID primary keys throughout
- Comprehensive indexing for performance
- Automatic `updated_at` triggers
- Order number generation function
- JSONB for flexible data (customizations, nutritional info)

## Environment Configuration

**First-time setup**:
1. Copy `.env.example` to `.env`
2. Configure required authentication keys (see below)
3. Run `./start.sh` to initialize all services

**Required environment variables**:
```bash
# Database (auto-configured in Docker)
POSTGRES_DB=fastfood_db
POSTGRES_USER=admin  
POSTGRES_PASSWORD=secure_password

# Clerk Authentication (required) - Get from https://dashboard.clerk.dev/
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# Mercado Pago (required for payments) - Get from https://www.mercadopago.com/developers/
MERCADO_PAGO_ACCESS_TOKEN=TEST-...
NEXT_PUBLIC_MERCADO_PAGO_PUBLIC_KEY=TEST-...

# API URLs (auto-configured for local development)
NEXT_PUBLIC_API_URL=http://localhost:8000
```

**Note**: The `start.sh` script will automatically copy `.env.example` to `.env` if it doesn't exist and prompt you to configure the required keys.

## Authentication Flow

1. User registers/logs in via Clerk
2. Clerk provides JWT token to frontend
3. Frontend includes token in API request headers
4. Backend validates token with Clerk API
5. User roles determine authorization (customer/admin/staff)

## Payment Integration (Mercado Pago)

**Standard Flow**:
1. Customer confirms order in frontend
2. Backend creates Mercado Pago payment preference
3. User redirected to Mercado Pago checkout
4. Webhook receives payment confirmation
5. Order status updated accordingly
6. Customer notification sent

**Order States**: PENDING � PAID � PREPARING � READY � DELIVERED

## Development Guidelines

### Code Style
- **TypeScript**: Strict mode enabled
- **ESLint + Prettier**: Automated formatting via lint-staged and Husky pre-commit hooks
- **Tailwind CSS**: Utility-first styling with consistent design system
- **Component Structure**: Radix UI primitives + custom components
- **Pre-commit Hooks**: Automatic code formatting and linting on commit

### API Development
- **RESTful Endpoints**: Consistent HTTP methods and status codes
- **Request Validation**: Pydantic schemas for all inputs
- **Error Handling**: Structured HTTP exceptions
- **Documentation**: Automatic OpenAPI via FastAPI

### Testing Strategy (Planned)
- **Frontend**: Jest + Testing Library for components
- **Backend**: Pytest for API endpoints
- **Integration**: Docker-based test environments

## Current Development Status

**Completed (82%)**:
-  Complete project documentation and architecture
-  Database schema with 9 tables and relationships  
-  Docker containerization with multi-stage builds
-  Development environment configuration
-  Dependency management and tooling setup

**Next Implementation Phase**:
- Backend API implementation (models, schemas, endpoints)
- Frontend component development and pages
- Authentication integration (Clerk)
- Payment system integration (Mercado Pago)
- Admin dashboard development

## Troubleshooting

### Common Issues
- **Docker build failures**: Check memory allocation (increase to 2.8GB+)
- **Database connection**: Ensure PostgreSQL service is healthy
- **Port conflicts**: Stop other services using ports 3000, 8000, 5432, 8080

### Useful Debug Commands
```bash
# Service status
docker-compose ps

# Service logs  
docker-compose logs -f [service_name]

# Rebuild specific service
docker-compose build --no-cache [service_name]

# Database connection test
docker-compose exec database pg_isready -U admin -d fastfood_db
```

## Important Development Commands

**ALWAYS run these commands when implementing new features**:
```bash
# Frontend linting and type checking (run from frontend/ directory)
npm run lint && npm run type-check

# Backend formatting and linting (run from backend/ directory when requirements-dev.txt is available)
black . && isort . && flake8 . && mypy .

# Build verification (before committing changes)
docker-compose build --no-cache
```

**Single test execution examples**:
```bash
# Frontend
npm test -- --testNamePattern="ComponentName"
npm test -- src/components/Button.test.tsx

# Backend (when implemented)
pytest tests/test_products.py::test_create_product
pytest -k "test_authentication"
```

## Key Files for Development

- `SYSTEM_PROMPTS.md`: Development guidelines for each technology
- `ARCHITECTURE.md`: Detailed system architecture and diagrams  
- `TODO.md`: Project status and remaining tasks
- `database/schema.sql`: Complete database schema (380 lines)
- `docker-compose.yml`: Multi-service container configuration
- `start.sh`: Automated development environment setup