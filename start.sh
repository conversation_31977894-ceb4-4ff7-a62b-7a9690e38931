#!/bin/bash

# =====================================================
# SaaS Comidas Rápidas - Script de Inicio Rápido
# =====================================================

set -e

echo "🍔 Iniciando SaaS Comidas Rápidas..."
echo "======================================"

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir con color
print_color() {
    printf "${1}${2}${NC}\n"
}

# Verificar que Docker esté instalado
if ! command -v docker &> /dev/null; then
    print_color $RED "❌ Docker no está instalado. Por favor instala Docker primero."
    exit 1
fi

# Verificar que Docker Compose esté instalado
if ! command -v docker-compose &> /dev/null; then
    print_color $RED "❌ Docker Compose no está instalado. Por favor instala Docker Compose primero."
    exit 1
fi

# Verificar si el archivo .env existe
if [ ! -f .env ]; then
    print_color $YELLOW "⚠️  Archivo .env no encontrado. Copiando desde .env.example..."
    cp .env.example .env
    print_color $YELLOW "📝 Por favor edita el archivo .env con tus credenciales reales antes de continuar."
    print_color $BLUE "   - Clerk keys (https://dashboard.clerk.dev/)"
    print_color $BLUE "   - Mercado Pago keys (https://www.mercadopago.com.ar/developers/)"
    echo ""
    read -p "¿Quieres continuar de todos modos? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

print_color $BLUE "🔧 Parando contenedores previos (si existen)..."
docker-compose down --remove-orphans

print_color $BLUE "🏗️  Construyendo imágenes Docker..."
docker-compose build --no-cache

print_color $BLUE "🚀 Levantando servicios..."
docker-compose up -d

# Esperar a que los servicios estén listos
print_color $YELLOW "⏳ Esperando a que los servicios estén listos..."
sleep 10

# Verificar estado de los servicios
print_color $BLUE "🔍 Verificando estado de los servicios..."

# Base de datos
if docker-compose exec -T database pg_isready -U admin -d fastfood_db > /dev/null 2>&1; then
    print_color $GREEN "✅ Base de datos PostgreSQL está lista"
else
    print_color $RED "❌ Base de datos no está respondiendo"
fi

# Backend
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    print_color $GREEN "✅ Backend FastAPI está listo"
else
    print_color $YELLOW "⚠️  Backend aún no está listo (normal en primer inicio)"
fi

# Frontend
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    print_color $GREEN "✅ Frontend Next.js está listo"
else
    print_color $YELLOW "⚠️  Frontend aún no está listo (normal en primer inicio)"
fi

echo ""
print_color $GREEN "🎉 ¡SaaS Comidas Rápidas iniciado exitosamente!"
echo "======================================"
print_color $BLUE "📱 Frontend:     http://localhost:3000"
print_color $BLUE "🔧 Backend API:  http://localhost:8000"
print_color $BLUE "📊 API Docs:     http://localhost:8000/docs"
print_color $BLUE "🗄️  Adminer:     http://localhost:8080"
echo ""
print_color $YELLOW "📝 Credenciales de Adminer:"
print_color $YELLOW "   - Sistema: PostgreSQL"
print_color $YELLOW "   - Servidor: database"
print_color $YELLOW "   - Usuario: admin"
print_color $YELLOW "   - Contraseña: (ver .env)"
print_color $YELLOW "   - Base de datos: fastfood_db"
echo ""
print_color $BLUE "📋 Para ver logs:"
print_color $BLUE "   docker-compose logs -f [servicio]"
echo ""
print_color $BLUE "🛑 Para parar:"
print_color $BLUE "   docker-compose down"
echo ""
print_color $GREEN "¡Feliz desarrollo! 🚀"