# =====================================================
# SaaS Comidas Rápidas - Variables de Entorno
# =====================================================

# =====================================================
# BASE DE DATOS POSTGRESQL
# =====================================================
POSTGRES_DB=fastfood_db
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password_change_in_production
DATABASE_URL=*********************************************************************/fastfood_db

# =====================================================
# CLERK AUTHENTICATION
# =====================================================
# Obtener desde: https://dashboard.clerk.dev/
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here

# URLs de redirección de Clerk
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# =====================================================
# MERCADO PAGO
# =====================================================
# Obtener desde: https://www.mercadopago.com.ar/developers/
# IMPORTANTE: Usar credenciales de TEST para desarrollo
MERCADO_PAGO_ACCESS_TOKEN=TEST-your_access_token_here
NEXT_PUBLIC_MERCADO_PAGO_PUBLIC_KEY=TEST-your_public_key_here

# URLs de webhook (cambiar en producción)
MERCADO_PAGO_WEBHOOK_URL=http://localhost:8000/api/v1/payments/webhook
MERCADO_PAGO_SUCCESS_URL=http://localhost:3000/payment/success
MERCADO_PAGO_FAILURE_URL=http://localhost:3000/payment/failure
MERCADO_PAGO_PENDING_URL=http://localhost:3000/payment/pending

# =====================================================
# URLs DE LA APLICACIÓN
# =====================================================
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3000

# CORS Origins (separados por coma)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000

# =====================================================
# CONFIGURACIÓN DE LA APLICACIÓN
# =====================================================
APP_ENV=development
NODE_ENV=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-in-production-minimum-32-characters
LOG_LEVEL=info

# Next.js
NEXT_TELEMETRY_DISABLED=1

# =====================================================
# REDIS (OPCIONAL - PARA CACHE)
# =====================================================
REDIS_PASSWORD=redis_secure_password_change_in_production
REDIS_URL=redis://:redis_secure_password_change_in_production@redis:6379/0

# =====================================================
# CONFIGURACIÓN DE EMAIL (OPCIONAL)
# =====================================================
# Para notificaciones por email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME="FastFood App"

# =====================================================
# CONFIGURACIÓN DE ARCHIVOS (FUTURO)
# =====================================================
# Para subida de imágenes
MAX_FILE_SIZE=5242880  # 5MB en bytes
ALLOWED_FILE_TYPES=jpg,jpeg,png,webp
UPLOAD_PATH=/uploads

# =====================================================
# CONFIGURACIÓN DE SEGURIDAD
# =====================================================
# JWT Settings
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# =====================================================
# CONFIGURACIÓN DE PRODUCCIÓN
# =====================================================
# Cambiar estos valores en producción

# SSL/HTTPS
FORCE_HTTPS=false
SECURE_COOKIES=false

# Dominio de producción
PRODUCTION_DOMAIN=yourapp.com

# Configuración de proxy (si usas Nginx/Cloudflare)
TRUST_PROXY=false

# =====================================================
# CONFIGURACIÓN DE ANALYTICS (OPCIONAL)
# =====================================================
# Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# =====================================================
# NOTAS IMPORTANTES
# =====================================================
# 1. NUNCA commitear este archivo con valores reales
# 2. Copiar a .env y completar con valores reales
# 3. En producción, usar variables de entorno del servidor
# 4. Cambiar todas las passwords por valores seguros
# 5. Las claves de Mercado Pago de TEST solo funcionan en sandbox