# Arquitectura del Sistema - SaaS Comidas Rápidas

## 🏗️ Visión General de la Arquitectura

### Patrón Arquitectural
- **Microservicios**: Separación clara entre frontend y backend
- **API-First**: Backend expone APIs RESTful
- **Event-Driven**: Manejo de eventos para pedidos y pagos
- **Stateless**: Servicios sin estado para escalabilidad

## 🔄 Flujo de Datos

```
[Cliente] → [Next.js] → [FastAPI] → [PostgreSQL]
                ↓
            [Clerk Auth] → [Mercado Pago]
```

## 📊 Diagrama de Componentes

```
┌─────────────────────────────────────────────────┐
│                 FRONTEND LAYER                  │
│  ┌─────────────┐  ┌─────────────┐              │
│  │   Next.js   │  │   Clerk     │              │
│  │   App       │  │   Auth      │              │
│  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────┘
                        │
                    HTTP/REST
                        │
┌─────────────────────────────────────────────────┐
│                 BACKEND LAYER                   │
│  ┌─────────────┐  ┌─────────────┐              │
│  │   FastAPI   │  │  Mercado    │              │
│  │   Server    │  │  Pago API   │              │
│  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────┘
                        │
                    SQL/TCP
                        │
┌─────────────────────────────────────────────────┐
│                  DATA LAYER                     │
│  ┌─────────────┐  ┌─────────────┐              │
│  │ PostgreSQL  │  │   Adminer   │              │
│  │  Database   │  │   Admin     │              │
│  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────┘
```

## 🗂️ Estructura de Directorios

```
project-pedidos_A4/
├── frontend/                    # Next.js Application
│   ├── src/
│   │   ├── app/                # App Router (Next.js 14)
│   │   │   ├── (auth)/        # Auth layout group
│   │   │   ├── dashboard/     # Dashboard routes
│   │   │   ├── menu/          # Menu routes
│   │   │   └── layout.tsx     # Root layout
│   │   ├── components/        # Reusable components
│   │   │   ├── ui/           # UI components
│   │   │   ├── forms/        # Form components
│   │   │   └── layout/       # Layout components
│   │   ├── lib/              # Utilities and configs
│   │   │   ├── api.ts        # API client
│   │   │   ├── auth.ts       # Clerk config
│   │   │   └── utils.ts      # Helper functions
│   │   └── types/            # TypeScript types
│   ├── public/               # Static assets
│   └── package.json
│
├── backend/                     # FastAPI Application
│   ├── app/
│   │   ├── api/              # API routes
│   │   │   ├── v1/           # API version 1
│   │   │   │   ├── auth/     # Authentication endpoints
│   │   │   │   ├── products/ # Products CRUD
│   │   │   │   ├── orders/   # Orders management
│   │   │   │   └── payments/ # Payment processing
│   │   ├── core/             # Core functionality
│   │   │   ├── config.py     # App configuration
│   │   │   ├── security.py   # Security utilities
│   │   │   └── database.py   # DB connection
│   │   ├── models/           # SQLAlchemy models
│   │   │   ├── user.py
│   │   │   ├── product.py
│   │   │   ├── order.py
│   │   │   └── payment.py
│   │   ├── schemas/          # Pydantic schemas
│   │   │   ├── user.py
│   │   │   ├── product.py
│   │   │   └── order.py
│   │   └── services/         # Business logic
│   │       ├── auth_service.py
│   │       ├── order_service.py
│   │       └── payment_service.py
│   ├── migrations/           # Alembic migrations
│   ├── tests/               # Test files
│   └── requirements.txt
│
├── database/                   # Database files
│   └── init.sql              # Initial schema
│
├── docker-compose.yml          # Docker services
├── .env.example               # Environment template
└── README.md                  # Documentation
```

## 🔐 Seguridad y Autenticación

### Flujo de Autenticación
1. Usuario se registra/login en Clerk
2. Clerk genera JWT token
3. Frontend incluye token en headers
4. Backend valida token con Clerk
5. Autorización basada en roles

### Roles de Usuario
- **Customer**: Acceso a menú y pedidos
- **Admin**: Acceso completo al dashboard
- **Staff**: Gestión de pedidos (futuro)

## 💳 Integración de Pagos

### Flujo de Pago con Mercado Pago
```
1. Cliente confirma pedido
2. Frontend crea preferencia de pago
3. Redirección a Mercado Pago
4. Webhook confirma pago
5. Backend actualiza estado del pedido
6. Notificación al cliente
```

### Estados de Pedido
- `PENDING`: Creado, esperando pago
- `PAID`: Pago confirmado
- `PREPARING`: En preparación
- `READY`: Listo para entrega
- `DELIVERED`: Entregado
- `CANCELLED`: Cancelado

## 📡 APIs y Endpoints

### Autenticación (`/api/v1/auth/`)
- `POST /register` - Registro de usuario
- `POST /login` - Inicio de sesión
- `GET /profile` - Perfil del usuario

### Productos (`/api/v1/products/`)
- `GET /` - Listar productos
- `GET /{id}` - Obtener producto
- `POST /` - Crear producto (admin)
- `PUT /{id}` - Actualizar producto (admin)
- `DELETE /{id}` - Eliminar producto (admin)

### Pedidos (`/api/v1/orders/`)
- `GET /` - Listar pedidos del usuario
- `POST /` - Crear nuevo pedido
- `GET /{id}` - Obtener pedido específico
- `PUT /{id}/status` - Actualizar estado (admin)

### Pagos (`/api/v1/payments/`)
- `POST /create-preference` - Crear preferencia MP
- `POST /webhook` - Webhook de confirmación
- `GET /status/{order_id}` - Estado del pago

## 📊 Base de Datos

### Modelo Relacional
```sql
Users (id, clerk_id, email, role, created_at)
Categories (id, name, description, active)
Products (id, name, description, price, category_id, image_url, active)
Orders (id, user_id, total, status, created_at, updated_at)
OrderItems (id, order_id, product_id, quantity, price, customizations)
Payments (id, order_id, mp_payment_id, amount, status, created_at)
Promotions (id, name, discount_type, discount_value, active, expires_at)
```

## 🚀 Despliegue y Escalabilidad

### Contenedores Docker
- **frontend**: Next.js en contenedor optimizado
- **backend**: FastAPI con uvicorn
- **database**: PostgreSQL oficial
- **adminer**: Interfaz web para DB

### Variables de Entorno
```env
# Database
POSTGRES_DB=fastfood_db
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password
DATABASE_URL=***********************************/fastfood_db

# Authentication
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...

# Payments
MERCADO_PAGO_ACCESS_TOKEN=TEST-...
MERCADO_PAGO_PUBLIC_KEY=TEST-...

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

## 🔄 CI/CD Pipeline (Futuro)
1. **Desarrollo**: Feature branches
2. **Testing**: Automated tests
3. **Staging**: Pre-production environment
4. **Producción**: Automated deployment

## 📈 Monitoreo y Logging
- **Logs estructurados**: JSON format
- **Health checks**: Endpoint `/health`
- **Métricas**: Prometheus + Grafana (futuro)
- **Error tracking**: Sentry integration (futuro)

---
*Arquitectura diseñada para escalabilidad y mantenibilidad*