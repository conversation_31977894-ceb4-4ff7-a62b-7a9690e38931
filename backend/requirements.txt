# =====================================================
# FastAPI Backend - Dependencias de Producción
# =====================================================

# Framework principal
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Base de datos
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Validación y serialización
pydantic==2.5.0
pydantic-settings==2.1.0

# Autenticación
clerk-backend-api==0.6.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Pagos
mercadopago==2.2.1

# Utilidades
python-multipart==0.0.6
python-dotenv==1.0.0
httpx==0.25.2

# CORS
fastapi-cors==0.0.6

# Cache (opcional)
redis==5.0.1
fastapi-cache2==0.2.1

# Logging
loguru==0.7.2

# Validación de emails
email-validator==2.1.0

# Fechas y timezone
python-dateutil==2.8.2
pytz==2023.3

# Generación de IDs únicos
shortuuid==1.0.11

# Manejo de archivos e imágenes
pillow==10.1.0
python-magic==0.4.27

# Rate limiting
slowapi==0.1.9

# Configuración
pydantic-settings==2.1.0

# Testing utilities (para health checks)
requests==2.31.0