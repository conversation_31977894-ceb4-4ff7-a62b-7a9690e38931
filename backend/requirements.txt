# =====================================================
# FastAPI Backend - Dependencias de Producción
# =====================================================

# Framework principal
fastapi>=0.104.0,<0.115.0
uvicorn[standard]>=0.24.0,<0.25.0

# Base de datos
sqlalchemy>=2.0.23,<2.1.0
psycopg2-binary>=2.9.9,<3.0.0
alembic>=1.12.1,<2.0.0

# Validación y serialización
pydantic>=2.7.0,<3.0.0
pydantic-settings>=2.2.0,<3.0.0

# Autenticación
clerk-backend-api>=3.0.0,<4.0.0
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.4,<2.0.0

# Pagos
mercadopago>=2.2.0,<3.0.0

# Utilidades
python-multipart>=0.0.6,<1.0.0
python-dotenv>=1.0.0,<2.0.0
httpx>=0.25.0,<1.0.0

# CORS
fastapi-cors==0.0.6

# Cache (opcional)
redis==5.0.1
fastapi-cache2==0.2.2

# Logging
loguru==0.7.2

# Validación de emails
email-validator==2.1.0

# Fechas y timezone
python-dateutil==2.8.2
pytz==2023.3

# Generación de IDs únicos
shortuuid==1.0.11

# Manejo de archivos e imágenes
pillow==10.1.0
python-magic==0.4.27

# Rate limiting
slowapi==0.1.9

# Configuración ya definida arriba

# Testing utilities (para health checks)
requests==2.31.0