# =====================================================
# Backend FastAPI - Multi-stage Dockerfile
# =====================================================

FROM python:3.11-slim as base

# Variables de entorno
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_TIMEOUT=300

# Crear usuario no-root
RUN groupadd -r fastfood && useradd -r -g fastfood fastfood

# Instalar dependencias del sistema necesarias para compilar paquetes Python
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    python3-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# =====================================================
# Stage: Dependencias
# =====================================================
FROM base as dependencies

WORKDIR /app

# Copiar archivos de requirements
COPY requirements.txt .
COPY requirements-dev.txt .

# Instalar dependencias de Python con configuración optimizada
RUN pip install --upgrade pip && \
    pip install --timeout=300 --no-cache-dir -r requirements.txt

# =====================================================
# Stage: Desarrollo
# =====================================================
FROM dependencies as development

# Instalar dependencias de desarrollo
RUN pip install -r requirements-dev.txt

# Copiar código fuente
COPY . .

# Cambiar ownership
RUN chown -R fastfood:fastfood /app

# Cambiar a usuario no-root  
USER fastfood

# Puerto de la aplicación
EXPOSE 8000

# Comando de desarrollo con hot reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =====================================================
# Stage: Producción
# =====================================================
FROM dependencies as production

# Copiar solo los archivos necesarios
COPY app/ ./app/

# Crear directorios necesarios
RUN mkdir -p /app/logs && \
    chown -R fastfood:fastfood /app

# Cambiar a usuario no-root
USER fastfood

# Puerto de la aplicación
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Comando de producción
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]