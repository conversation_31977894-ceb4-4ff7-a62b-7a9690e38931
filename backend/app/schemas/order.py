"""
Order schemas
"""
import uuid
from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, Field
from app.schemas.base import BaseSchema, BaseModelSchema


class OrderItemBase(BaseSchema):
    """Base order item schema"""
    product_id: uuid.UUID
    product_name: str
    product_price: Decimal = Field(..., ge=0, decimal_places=2)
    quantity: int = Field(..., ge=1)
    unit_price: Decimal = Field(..., ge=0, decimal_places=2)
    total_price: Decimal = Field(..., ge=0, decimal_places=2)
    customizations: Optional[Dict[str, Any]] = None
    special_requests: Optional[str] = None


class OrderItemCreate(OrderItemBase):
    """Schema for order item creation"""
    pass


class OrderItemResponse(BaseModelSchema, OrderItemBase):
    """Schema for order item response"""
    order_id: uuid.UUID


class OrderBase(BaseSchema):
    """Base order schema"""
    subtotal: Decimal = Field(..., ge=0, decimal_places=2)
    discount_amount: Decimal = Field(default=0, ge=0, decimal_places=2)
    tax_amount: Decimal = Field(default=0, ge=0, decimal_places=2)
    delivery_fee: Decimal = Field(default=0, ge=0, decimal_places=2)
    total_amount: Decimal = Field(..., ge=0, decimal_places=2)
    status: str = "pending"
    order_type: str = "delivery"
    estimated_preparation_time: Optional[int] = None
    estimated_delivery_time: Optional[datetime] = None
    special_instructions: Optional[str] = None


class OrderCreate(OrderBase):
    """Schema for order creation"""
    delivery_address_id: Optional[uuid.UUID] = None
    promotion_id: Optional[uuid.UUID] = None
    order_items: List[OrderItemCreate]


class OrderUpdate(BaseSchema):
    """Schema for order updates"""
    status: Optional[str] = None
    estimated_preparation_time: Optional[int] = None
    estimated_delivery_time: Optional[datetime] = None
    special_instructions: Optional[str] = None


class OrderResponse(BaseModelSchema, OrderBase):
    """Schema for order response"""
    order_number: str
    user_id: Optional[uuid.UUID] = None
    delivery_address_id: Optional[uuid.UUID] = None
    promotion_id: Optional[uuid.UUID] = None
    confirmed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    order_items: List[OrderItemResponse] = []


class OrderListResponse(BaseSchema):
    """Schema for order list response"""
    orders: list[OrderResponse]
    total: int
    page: int
    size: int