"""
Payment schemas
"""
import uuid
from typing import Optional
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, Field
from app.schemas.base import BaseSchema, BaseModelSchema


class PaymentBase(BaseSchema):
    """Base payment schema"""
    amount: Decimal = Field(..., ge=0, decimal_places=2)
    currency: str = "ARS"
    payment_method: Optional[str] = None
    payment_type: Optional[str] = None
    status: str = "pending"
    external_reference: Optional[str] = None


class PaymentCreate(PaymentBase):
    """Schema for payment creation"""
    order_id: uuid.UUID


class PaymentUpdate(BaseSchema):
    """Schema for payment updates"""
    mp_payment_id: Optional[str] = None
    mp_preference_id: Optional[str] = None
    payment_method: Optional[str] = None
    payment_type: Optional[str] = None
    status: Optional[str] = None
    failure_reason: Optional[str] = None
    processed_at: Optional[datetime] = None


class PaymentResponse(BaseModelSchema, PaymentBase):
    """Schema for payment response"""
    order_id: uuid.UUID
    mp_payment_id: Optional[str] = None
    mp_preference_id: Optional[str] = None
    failure_reason: Optional[str] = None
    processed_at: Optional[datetime] = None


class PaymentListResponse(BaseSchema):
    """Schema for payment list response"""
    payments: list[PaymentResponse]
    total: int
    page: int
    size: int