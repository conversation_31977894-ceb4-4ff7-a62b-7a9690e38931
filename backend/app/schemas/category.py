"""
Category schemas
"""
from typing import Optional
from pydantic import BaseModel
from app.schemas.base import BaseSchema, BaseModelSchema


class CategoryBase(BaseSchema):
    """Base category schema"""
    name: str
    description: Optional[str] = None
    image_url: Optional[str] = None
    sort_order: int = 0
    is_active: bool = True


class CategoryCreate(CategoryBase):
    """Schema for category creation"""
    pass


class CategoryUpdate(BaseSchema):
    """Schema for category updates"""
    name: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None


class CategoryResponse(BaseModelSchema, CategoryBase):
    """Schema for category response"""
    pass


class CategoryListResponse(BaseSchema):
    """Schema for category list response"""
    categories: list[CategoryResponse]
    total: int