"""
User schemas
"""
from typing import Optional
from pydantic import BaseModel, EmailStr
from app.schemas.base import BaseSchema, BaseModelSchema


class UserBase(BaseSchema):
    """Base user schema"""
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    role: str = "customer"
    is_active: bool = True


class UserCreate(UserBase):
    """Schema for user creation"""
    clerk_id: str


class UserUpdate(BaseSchema):
    """Schema for user updates"""
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(BaseModelSchema, UserBase):
    """Schema for user response"""
    clerk_id: str


class UserListResponse(BaseSchema):
    """Schema for user list response"""
    users: list[UserResponse]
    total: int
    page: int
    size: int