"""
Product schemas
"""
import uuid
from typing import Optional, List, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field
from app.schemas.base import BaseSchema, BaseModelSchema


class ProductBase(BaseSchema):
    """Base product schema"""
    name: str = Field(..., max_length=150)
    description: Optional[str] = None
    price: Decimal = Field(..., ge=0, decimal_places=2)
    image_url: Optional[str] = None
    preparation_time: int = Field(default=15, ge=1)
    is_available: bool = True
    is_featured: bool = False
    ingredients: Optional[List[str]] = None
    allergens: Optional[List[str]] = None
    nutritional_info: Optional[Dict[str, Any]] = None
    customization_options: Optional[Dict[str, Any]] = None
    sort_order: int = 0


class ProductCreate(ProductBase):
    """Schema for product creation"""
    category_id: Optional[uuid.UUID] = None


class ProductUpdate(BaseSchema):
    """Schema for product updates"""
    category_id: Optional[uuid.UUID] = None
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = Field(None, ge=0, decimal_places=2)
    image_url: Optional[str] = None
    preparation_time: Optional[int] = Field(None, ge=1)
    is_available: Optional[bool] = None
    is_featured: Optional[bool] = None
    ingredients: Optional[List[str]] = None
    allergens: Optional[List[str]] = None
    nutritional_info: Optional[Dict[str, Any]] = None
    customization_options: Optional[Dict[str, Any]] = None
    sort_order: Optional[int] = None


class ProductResponse(BaseModelSchema, ProductBase):
    """Schema for product response"""
    category_id: Optional[uuid.UUID] = None


class ProductListResponse(BaseSchema):
    """Schema for product list response"""
    products: list[ProductResponse]
    total: int
    page: int
    size: int