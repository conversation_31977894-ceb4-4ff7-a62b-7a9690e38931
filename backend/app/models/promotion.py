"""
Promotion model
"""
from sqlalchemy import Column, String, Text, Numeric, Integer, Boolean, DateTime, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class Promotion(BaseModel):
    __tablename__ = "promotions"
    
    name = Column(String(150), nullable=False)
    description = Column(Text)
    discount_type = Column(String(20), nullable=False)  # 'percentage' or 'fixed_amount'
    discount_value = Column(Numeric(10, 2), nullable=False)
    min_order_amount = Column(Numeric(10, 2), default=0)
    max_discount_amount = Column(Numeric(10, 2))
    usage_limit = Column(Integer)
    used_count = Column(Integer, default=0)
    applicable_products = Column(ARRAY(PostgresUUID(as_uuid=True)))
    applicable_categories = Column(ARRAY(PostgresUUID(as_uuid=True)))
    is_active = Column(Boolean, default=True, nullable=False)
    starts_at = Column(DateTime(timezone=True), nullable=False)
    expires_at = Column(DateTime(timezone=True))
    
    # Relationships
    orders = relationship("Order", back_populates="promotion")
    
    def __repr__(self):
        return f"<Promotion(name='{self.name}', discount_type='{self.discount_type}')>"