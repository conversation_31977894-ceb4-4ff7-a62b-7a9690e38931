"""
Order Item model
"""
from sqlalchemy import Column, String, Numeric, Integer, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class OrderItem(BaseModel):
    __tablename__ = "order_items"
    
    order_id = Column(PostgresUUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True)
    product_id = Column(PostgresUUID(as_uuid=True), ForeignKey("products.id"), index=True)
    
    # Product info at order time
    product_name = Column(String(150), nullable=False)
    product_price = Column(Numeric(10, 2), nullable=False)
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # Customizations
    customizations = Column(JSONB)
    special_requests = Column(Text)
    
    # Relationships
    order = relationship("Order", back_populates="order_items")
    product = relationship("Product", back_populates="order_items")
    
    def __repr__(self):
        return f"<OrderItem(product_name='{self.product_name}', quantity={self.quantity})>"