"""
Order Status History model
"""
from sqlalchemy import Column, String, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class OrderStatusHistory(BaseModel):
    __tablename__ = "order_status_history"
    
    order_id = Column(PostgresUUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True)
    status = Column(String(20), nullable=False)
    notes = Column(Text)
    changed_by = Column(PostgresUUID(as_uuid=True), ForeignKey("users.id"))
    
    # Relationships
    order = relationship("Order", back_populates="status_history")
    changed_by_user = relationship("User", foreign_keys=[changed_by])
    
    def __repr__(self):
        return f"<OrderStatusHistory(status='{self.status}')>"