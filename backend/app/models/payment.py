"""
Payment model
"""
from sqlalchemy import Column, String, Numeric, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class Payment(BaseModel):
    __tablename__ = "payments"
    
    order_id = Column(PostgresUUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True)
    
    # Mercado Pago info
    mp_payment_id = Column(String(100), unique=True, index=True)
    mp_preference_id = Column(String(100))
    
    # Payment details
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), default="ARS")
    payment_method = Column(String(50))
    payment_type = Column(String(50))
    
    # Status
    status = Column(String(20), default="pending", nullable=False, index=True)
    
    # Additional info
    failure_reason = Column(Text)
    external_reference = Column(String(100))
    
    # Timestamps
    processed_at = Column(DateTime(timezone=True))
    
    # Relationships
    order = relationship("Order", back_populates="payments")
    
    def __repr__(self):
        return f"<Payment(mp_payment_id='{self.mp_payment_id}', status='{self.status}')>"