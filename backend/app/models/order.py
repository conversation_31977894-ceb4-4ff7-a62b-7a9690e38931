"""
Order model
"""
from sqlalchemy import Column, String, Numeric, Integer, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class Order(BaseModel):
    __tablename__ = "orders"
    
    order_number = Column(String(20), unique=True, nullable=False, index=True)
    user_id = Column(PostgresUUID(as_uuid=True), ForeignKey("users.id"), index=True)
    delivery_address_id = Column(PostgresUUID(as_uuid=True), ForeignKey("delivery_addresses.id"))
    
    # Order amounts
    subtotal = Column(Numeric(10, 2), nullable=False)
    discount_amount = Column(Numeric(10, 2), default=0)
    tax_amount = Column(Numeric(10, 2), default=0)
    delivery_fee = Column(Numeric(10, 2), default=0)
    total_amount = Column(Numeric(10, 2), nullable=False)
    
    # Status and type
    status = Column(String(20), default="pending", nullable=False, index=True)
    order_type = Column(String(20), default="delivery", nullable=False)
    
    # Timing
    estimated_preparation_time = Column(Integer)
    estimated_delivery_time = Column(DateTime(timezone=True))
    
    # Additional info
    special_instructions = Column(Text)
    promotion_id = Column(PostgresUUID(as_uuid=True), ForeignKey("promotions.id"))
    
    # Completion timestamps
    confirmed_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", back_populates="orders")
    delivery_address = relationship("DeliveryAddress", back_populates="orders")
    promotion = relationship("Promotion", back_populates="orders")
    order_items = relationship("OrderItem", back_populates="order")
    payments = relationship("Payment", back_populates="order")
    status_history = relationship("OrderStatusHistory", back_populates="order")
    
    def __repr__(self):
        return f"<Order(order_number='{self.order_number}', status='{self.status}')>"