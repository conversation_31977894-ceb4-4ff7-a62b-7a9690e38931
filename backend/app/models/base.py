"""
Base model classes
"""
import uuid
from datetime import datetime
from sqlalchemy import Column, DateTime, UUID
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from app.core.database import Base


class BaseModel(Base):
    """Base model with common fields"""
    __abstract__ = True
    
    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)