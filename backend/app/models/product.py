"""
Product model
"""
from sqlalchemy import Column, String, Text, Numeric, Integer, Boolean, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, JSONB
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class Product(BaseModel):
    __tablename__ = "products"
    
    category_id = Column(PostgresUUID(as_uuid=True), ForeignKey("categories.id"), index=True)
    name = Column(String(150), nullable=False)
    description = Column(Text)
    price = Column(Numeric(10, 2), nullable=False)
    image_url = Column(String(500))
    preparation_time = Column(Integer, default=15)
    is_available = Column(Boolean, default=True, nullable=False, index=True)
    is_featured = Column(Boolean, default=False, nullable=False, index=True)
    ingredients = Column(ARRAY(Text))
    allergens = Column(ARRAY(Text))
    nutritional_info = Column(JSONB)
    customization_options = Column(JSONB)
    sort_order = Column(Integer, default=0)
    
    # Relationships
    category = relationship("Category", back_populates="products")
    order_items = relationship("OrderItem", back_populates="product")
    favorites = relationship("UserFavorite", back_populates="product")
    
    def __repr__(self):
        return f"<Product(name='{self.name}', price={self.price})>"