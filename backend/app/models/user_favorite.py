"""
User Favorite model
"""
from sqlalchemy import Column, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class UserFavorite(BaseModel):
    __tablename__ = "user_favorites"
    
    user_id = Column(PostgresUUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    product_id = Column(PostgresUUID(as_uuid=True), ForeignKey("products.id"), nullable=False, index=True)
    
    # Relationships
    user = relationship("User", back_populates="favorites")
    product = relationship("Product", back_populates="favorites")
    
    # Unique constraint
    __table_args__ = (UniqueConstraint('user_id', 'product_id', name='unique_user_product_favorite'),)
    
    def __repr__(self):
        return f"<UserFavorite(user_id='{self.user_id}', product_id='{self.product_id}')>"