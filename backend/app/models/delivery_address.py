"""
Delivery Address model
"""
from sqlalchemy import Column, String, Text, Numeric, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class DeliveryAddress(BaseModel):
    __tablename__ = "delivery_addresses"
    
    user_id = Column(PostgresUUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    label = Column(String(50))
    street_address = Column(String(255), nullable=False)
    city = Column(String(100), nullable=False)
    state = Column(String(100))
    postal_code = Column(String(20))
    country = Column(String(100), default="Argentina")
    additional_info = Column(Text)
    latitude = Column(Numeric(10, 8))
    longitude = Column(Numeric(11, 8))
    is_default = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="delivery_addresses")
    orders = relationship("Order", back_populates="delivery_address")
    
    def __repr__(self):
        return f"<DeliveryAddress(label='{self.label}', city='{self.city}')>"