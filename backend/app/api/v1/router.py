"""
API Router principal v1
"""
from fastapi import APIRouter

# Import de routers específicos
from app.api.v1.endpoints import auth, categories, products, orders, payments, users, mercadopago

api_router = APIRouter()

# Incluir routers
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(orders.router, prefix="/orders", tags=["orders"])
api_router.include_router(payments.router, prefix="/payments", tags=["payments"])
api_router.include_router(mercadopago.router, prefix="/mercadopago", tags=["mercadopago"])

@api_router.get("/")
async def api_root():
    """Endpoint raíz de la API v1"""
    return {"message": "SaaS Comidas Rápidas API v1", "status": "active"}