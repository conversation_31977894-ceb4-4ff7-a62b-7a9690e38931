"""
Mercado Pago specific API endpoints
"""
import uuid
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_user_clerk_id
from app.models.order import Order
from app.models.payment import Payment
from app.models.user import User
from app.services.mercado_pago_service import MercadoPagoService
from app.services.payment_service import PaymentService
from app.schemas.payment import PaymentCreate

router = APIRouter()


@router.post("/create-preference/{order_id}")
async def create_payment_preference(
    order_id: uuid.UUID,
    clerk_id: str = Depends(get_current_user_clerk_id),
    db: Session = Depends(get_db)
):
    """Create Mercado Pago payment preference for an order"""
    # Get user
    user = db.query(User).filter(User.clerk_id == clerk_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Get order
    order = db.query(Order).filter(
        Order.id == order_id,
        Order.user_id == user.id
    ).first()
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    if order.status != "pending":
        raise HTTPException(status_code=400, detail="Order is not in pending status")
    
    try:
        # Create Mercado Pago service
        mp_service = MercadoPagoService()
        
        # Create payment preference
        preference_data = mp_service.create_payment_preference(order)
        
        # Create payment record
        payment_service = PaymentService(db)
        payment_create = PaymentCreate(
            order_id=order.id,
            amount=order.total_amount,
            currency="ARS",
            external_reference=order.order_number
        )
        payment = payment_service.create_payment(payment_create)
        
        # Update payment with MP preference ID
        payment.mp_preference_id = preference_data["preference_id"]
        db.commit()
        
        return {
            "preference_id": preference_data["preference_id"],
            "init_point": preference_data["init_point"],
            "sandbox_init_point": preference_data["sandbox_init_point"],
            "payment_id": payment.id,
            "order_number": order.order_number
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating payment preference: {str(e)}")


@router.post("/webhook")
async def mercadopago_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """Handle Mercado Pago webhook notifications"""
    try:
        # Get raw body and headers
        body = await request.body()
        webhook_data = await request.json()
        
        # TODO: Validate webhook signature
        # signature = request.headers.get("x-signature")
        # mp_service = MercadoPagoService()
        # if not mp_service.validate_webhook_signature(body.decode(), signature):
        #     raise HTTPException(status_code=401, detail="Invalid signature")
        
        if webhook_data.get("type") == "payment":
            mp_payment_id = webhook_data.get("data", {}).get("id")
            if not mp_payment_id:
                raise HTTPException(status_code=400, detail="Invalid webhook data")
            
            # Get payment from database
            payment = db.query(Payment).filter(
                Payment.mp_payment_id == str(mp_payment_id)
            ).first()
            
            if not payment:
                # Try to find by external reference
                mp_service = MercadoPagoService()
                payment_info = mp_service.get_payment_info(str(mp_payment_id))
                external_ref = payment_info.get("external_reference")
                
                if external_ref:
                    order = db.query(Order).filter(Order.order_number == external_ref).first()
                    if order:
                        payment = db.query(Payment).filter(Payment.order_id == order.id).first()
            
            if not payment:
                return {"message": "Payment not found, ignoring webhook"}
            
            # Update payment with MP information
            mp_service = MercadoPagoService()
            payment_info = mp_service.get_payment_info(str(mp_payment_id))
            
            payment_service = PaymentService(db)
            updated_payment = payment_service.update_payment_status(
                payment=payment,
                status=payment_info["status"],
                mp_payment_id=str(mp_payment_id),
                payment_method=payment_info.get("payment_method_id"),
                failure_reason=payment_info.get("status_detail") if payment_info["status"] == "rejected" else None
            )
            
            return {
                "message": "Webhook processed successfully",
                "payment_id": updated_payment.id,
                "status": updated_payment.status
            }
        
        return {"message": "Webhook type not handled"}
        
    except Exception as e:
        print(f"Webhook error: {e}")
        raise HTTPException(status_code=500, detail=f"Webhook processing error: {str(e)}")


@router.get("/payment-status/{payment_id}")
async def get_payment_status(
    payment_id: uuid.UUID,
    clerk_id: str = Depends(get_current_user_clerk_id),
    db: Session = Depends(get_db)
):
    """Get payment status from Mercado Pago"""
    # Get user
    user = db.query(User).filter(User.clerk_id == clerk_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Get payment
    payment = db.query(Payment).join(Order).filter(
        Payment.id == payment_id,
        Order.user_id == user.id
    ).first()
    
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    if not payment.mp_payment_id:
        return {
            "payment_id": payment.id,
            "status": payment.status,
            "amount": payment.amount,
            "currency": payment.currency
        }
    
    try:
        # Get latest status from Mercado Pago
        mp_service = MercadoPagoService()
        payment_info = mp_service.get_payment_info(payment.mp_payment_id)
        
        # Update payment status if different
        if payment_info["status"] != payment.status:
            payment_service = PaymentService(db)
            payment = payment_service.update_payment_status(
                payment=payment,
                status=payment_info["status"],
                payment_method=payment_info.get("payment_method_id"),
                failure_reason=payment_info.get("status_detail") if payment_info["status"] == "rejected" else None
            )
        
        return {
            "payment_id": payment.id,
            "mp_payment_id": payment.mp_payment_id,
            "status": payment.status,
            "amount": payment.amount,
            "currency": payment.currency,
            "payment_method": payment.payment_method,
            "processed_at": payment.processed_at,
            "failure_reason": payment.failure_reason
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting payment status: {str(e)}")


@router.post("/refund/{payment_id}")
async def create_refund(
    payment_id: uuid.UUID,
    amount: float = None,
    reason: str = "Customer request",
    clerk_id: str = Depends(get_current_user_clerk_id),
    db: Session = Depends(get_db)
):
    """Create a refund for a payment (admin only)"""
    # Get user and check admin role
    user = db.query(User).filter(User.clerk_id == clerk_id).first()
    if not user or user.role not in ["admin", "staff"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    # Get payment
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    if payment.status != "approved":
        raise HTTPException(status_code=400, detail="Can only refund approved payments")
    
    try:
        # Create refund through Mercado Pago
        mp_service = MercadoPagoService()
        refund_response = mp_service.create_refund(payment.mp_payment_id, amount)
        
        # Update payment status
        payment_service = PaymentService(db)
        updated_payment = payment_service.process_refund(
            payment=payment,
            refund_amount=amount,
            reason=reason
        )
        
        return {
            "message": "Refund created successfully",
            "refund_id": refund_response.get("id"),
            "payment_id": updated_payment.id,
            "refund_amount": refund_response.get("amount"),
            "status": updated_payment.status
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating refund: {str(e)}")