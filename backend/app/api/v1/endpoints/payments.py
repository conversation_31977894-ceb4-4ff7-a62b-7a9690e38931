"""
Payments API endpoints
"""
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.payment import Payment
from app.schemas.payment import PaymentCreate, PaymentUpdate, PaymentResponse, PaymentListResponse

router = APIRouter()


@router.get("/", response_model=PaymentListResponse)
async def get_payments(
    skip: int = Query(0, ge=0, description="Number of payments to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of payments to return"),
    order_id: Optional[uuid.UUID] = Query(None, description="Filter by order"),
    status: Optional[str] = Query(None, description="Filter by status"),
    db: Session = Depends(get_db)
):
    """Get all payments with pagination and filters"""
    query = db.query(Payment)
    
    if order_id:
        query = query.filter(Payment.order_id == order_id)
    
    if status:
        query = query.filter(Payment.status == status)
    
    query = query.order_by(Payment.created_at.desc())
    
    total = query.count()
    payments = query.offset(skip).limit(limit).all()
    
    return PaymentListResponse(
        payments=payments,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific payment by ID"""
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    return payment


@router.get("/mp/{mp_payment_id}", response_model=PaymentResponse)
async def get_payment_by_mp_id(
    mp_payment_id: str,
    db: Session = Depends(get_db)
):
    """Get a payment by Mercado Pago payment ID"""
    payment = db.query(Payment).filter(Payment.mp_payment_id == mp_payment_id).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    return payment


@router.post("/", response_model=PaymentResponse, status_code=201)
async def create_payment(
    payment_data: PaymentCreate,
    db: Session = Depends(get_db)
):
    """Create a new payment"""
    payment = Payment(**payment_data.model_dump())
    db.add(payment)
    db.commit()
    db.refresh(payment)
    return payment


@router.put("/{payment_id}", response_model=PaymentResponse)
async def update_payment(
    payment_id: uuid.UUID,
    payment_data: PaymentUpdate,
    db: Session = Depends(get_db)
):
    """Update a payment (usually from webhook)"""
    payment = db.query(Payment).filter(Payment.id == payment_id).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    update_data = payment_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(payment, field, value)
    
    db.commit()
    db.refresh(payment)
    return payment


@router.post("/webhook/mercadopago")
async def mercadopago_webhook(
    data: dict,
    db: Session = Depends(get_db)
):
    """Handle Mercado Pago webhook notifications"""
    # TODO: Implement webhook signature validation
    # TODO: Process payment status updates
    
    if data.get("type") == "payment":
        mp_payment_id = data.get("data", {}).get("id")
        if not mp_payment_id:
            raise HTTPException(status_code=400, detail="Invalid webhook data")
        
        payment = db.query(Payment).filter(Payment.mp_payment_id == str(mp_payment_id)).first()
        if not payment:
            return {"message": "Payment not found, ignoring webhook"}
        
        # TODO: Fetch payment details from Mercado Pago API
        # TODO: Update payment status based on MP response
        # TODO: Update order status if payment is approved
        
        return {"message": "Webhook processed successfully"}
    
    return {"message": "Webhook type not handled"}


@router.get("/order/{order_id}", response_model=PaymentListResponse)
async def get_order_payments(
    order_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get all payments for a specific order"""
    payments = db.query(Payment).filter(Payment.order_id == order_id).all()
    
    return PaymentListResponse(
        payments=payments,
        total=len(payments),
        page=1,
        size=len(payments)
    )