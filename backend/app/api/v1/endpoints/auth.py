"""
Authentication API endpoints
"""
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_user_clerk_id, get_current_user_data
from app.models.user import User
from app.schemas.user import UserCreate, UserResponse

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    clerk_id: str = Depends(get_current_user_clerk_id),
    db: Session = Depends(get_db)
):
    """Get current authenticated user"""
    user = db.query(User).filter(User.clerk_id == clerk_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


@router.post("/register", response_model=UserResponse, status_code=201)
async def register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """Register a new user (called after Clerk signup)"""
    # Check if user already exists
    existing_user = db.query(User).filter(User.clerk_id == user_data.clerk_id).first()
    if existing_user:
        return existing_user  # Return existing user instead of error
    
    # Check email uniqueness
    existing_email = db.query(User).filter(User.email == user_data.email).first()
    if existing_email:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    user = User(**user_data.model_dump())
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@router.post("/sync")
async def sync_user_with_clerk(
    user_data: Dict[str, Any] = Depends(get_current_user_data),
    db: Session = Depends(get_db)
):
    """Sync user data with Clerk (update profile from token)"""
    clerk_id = user_data.get("sub")
    if not clerk_id:
        raise HTTPException(status_code=400, detail="Invalid token")
    
    user = db.query(User).filter(User.clerk_id == clerk_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Update user data from Clerk token
    if "email" in user_data:
        user.email = user_data["email"]
    if "given_name" in user_data:
        user.first_name = user_data["given_name"]
    if "family_name" in user_data:
        user.last_name = user_data["family_name"]
    
    db.commit()
    db.refresh(user)
    
    return {"message": "User data synchronized successfully"}


@router.get("/profile")
async def get_user_profile(
    user_data: Dict[str, Any] = Depends(get_current_user_data)
):
    """Get user profile data from token"""
    return {
        "clerk_id": user_data.get("sub"),
        "email": user_data.get("email"),
        "first_name": user_data.get("given_name"),
        "last_name": user_data.get("family_name"),
        "role": user_data.get("role", "customer"),
        "token_data": user_data
    }