"""
Orders API endpoints
"""
import uuid
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.order import Order
from app.models.order_item import OrderItem
from app.schemas.order import OrderCreate, OrderUpdate, OrderResponse, OrderListResponse

router = APIRouter()


@router.get("/", response_model=OrderListResponse)
async def get_orders(
    skip: int = Query(0, ge=0, description="Number of orders to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of orders to return"),
    user_id: Optional[uuid.UUID] = Query(None, description="Filter by user"),
    status: Optional[str] = Query(None, description="Filter by status"),
    db: Session = Depends(get_db)
):
    """Get all orders with pagination and filters"""
    query = db.query(Order)
    
    if user_id:
        query = query.filter(Order.user_id == user_id)
    
    if status:
        query = query.filter(Order.status == status)
    
    query = query.order_by(Order.created_at.desc())
    
    total = query.count()
    orders = query.offset(skip).limit(limit).all()
    
    return OrderListResponse(
        orders=orders,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """Get a specific order by ID"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    return order


@router.post("/", response_model=OrderResponse, status_code=201)
async def create_order(
    order_data: OrderCreate,
    db: Session = Depends(get_db)
):
    """Create a new order"""
    # Extract order items from the request
    order_items_data = order_data.order_items
    order_dict = order_data.model_dump(exclude={'order_items'})
    
    # Create order
    order = Order(**order_dict)
    db.add(order)
    db.flush()  # Flush to get the order ID
    
    # Create order items
    for item_data in order_items_data:
        order_item = OrderItem(
            order_id=order.id,
            **item_data.model_dump()
        )
        db.add(order_item)
    
    db.commit()
    db.refresh(order)
    return order


@router.put("/{order_id}", response_model=OrderResponse)
async def update_order(
    order_id: uuid.UUID,
    order_data: OrderUpdate,
    db: Session = Depends(get_db)
):
    """Update an order"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    update_data = order_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(order, field, value)
    
    db.commit()
    db.refresh(order)
    return order


@router.put("/{order_id}/status")
async def update_order_status(
    order_id: uuid.UUID,
    status: str,
    notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Update order status"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    
    # Validate status
    valid_statuses = [
        'pending', 'paid', 'confirmed', 'preparing', 
        'ready', 'out_for_delivery', 'delivered', 'cancelled'
    ]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    old_status = order.status
    order.status = status
    
    # TODO: Create status history record
    # status_history = OrderStatusHistory(
    #     order_id=order.id,
    #     status=status,
    #     notes=notes,
    #     changed_by=current_user.id  # Will need auth integration
    # )
    # db.add(status_history)
    
    db.commit()
    
    return {
        "message": f"Order status updated from {old_status} to {status}",
        "order_id": order_id,
        "status": status
    }


@router.get("/user/{user_id}", response_model=OrderListResponse)
async def get_user_orders(
    user_id: uuid.UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get orders for a specific user"""
    query = db.query(Order).filter(Order.user_id == user_id)
    query = query.order_by(Order.created_at.desc())
    
    total = query.count()
    orders = query.offset(skip).limit(limit).all()
    
    return OrderListResponse(
        orders=orders,
        total=total,
        page=skip // limit + 1,
        size=limit
    )