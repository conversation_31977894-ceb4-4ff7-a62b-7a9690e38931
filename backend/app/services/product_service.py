"""
Product business logic service
"""
import uuid
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from app.models.product import Product
from app.models.category import Category
from app.models.user_favorite import UserFavorite
from app.schemas.product import ProductCreate, ProductUpdate


class ProductService:
    """Service for product-related business logic"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_available_products(
        self, 
        category_id: Optional[uuid.UUID] = None,
        skip: int = 0, 
        limit: int = 20
    ) -> List[Product]:
        """Get available products with optional category filter"""
        query = self.db.query(Product).filter(Product.is_available == True)
        
        if category_id:
            query = query.filter(Product.category_id == category_id)
        
        return (
            query.order_by(Product.sort_order, Product.name)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_featured_products(self, limit: int = 10) -> List[Product]:
        """Get featured products"""
        return (
            self.db.query(Product)
            .filter(Product.is_featured == True, Product.is_available == True)
            .order_by(Product.sort_order, Product.name)
            .limit(limit)
            .all()
        )
    
    def search_products(self, search_term: str, limit: int = 20) -> List[Product]:
        """Search products by name and description"""
        search_pattern = f"%{search_term}%"
        return (
            self.db.query(Product)
            .filter(
                Product.is_available == True,
                (Product.name.ilike(search_pattern) | 
                 Product.description.ilike(search_pattern))
            )
            .order_by(Product.name)
            .limit(limit)
            .all()
        )
    
    def create_product(self, product_data: ProductCreate) -> Product:
        """Create a new product"""
        # Validate category exists if provided
        if product_data.category_id:
            category = self.db.query(Category).filter(
                Category.id == product_data.category_id,
                Category.is_active == True
            ).first()
            if not category:
                raise ValueError("Category not found or inactive")
        
        product = Product(**product_data.model_dump())
        self.db.add(product)
        self.db.commit()
        self.db.refresh(product)
        return product
    
    def update_product(self, product: Product, product_data: ProductUpdate) -> Product:
        """Update product data"""
        # Validate category if being updated
        if product_data.category_id:
            category = self.db.query(Category).filter(
                Category.id == product_data.category_id,
                Category.is_active == True
            ).first()
            if not category:
                raise ValueError("Category not found or inactive")
        
        update_data = product_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(product, field, value)
        
        self.db.commit()
        self.db.refresh(product)
        return product
    
    def toggle_product_availability(self, product: Product) -> Product:
        """Toggle product availability"""
        product.is_available = not product.is_available
        self.db.commit()
        self.db.refresh(product)
        return product
    
    def set_featured_products(self, product_ids: List[uuid.UUID]) -> List[Product]:
        """Set specific products as featured"""
        # First, remove featured status from all products
        self.db.query(Product).update({"is_featured": False})
        
        # Then set featured status for specified products
        products = (
            self.db.query(Product)
            .filter(Product.id.in_(product_ids))
            .all()
        )
        
        for product in products:
            product.is_featured = True
        
        self.db.commit()
        return products
    
    def add_to_favorites(self, user_id: uuid.UUID, product_id: uuid.UUID) -> bool:
        """Add product to user favorites"""
        # Check if already favorited
        existing = (
            self.db.query(UserFavorite)
            .filter(
                UserFavorite.user_id == user_id,
                UserFavorite.product_id == product_id
            )
            .first()
        )
        
        if existing:
            return False  # Already favorited
        
        favorite = UserFavorite(user_id=user_id, product_id=product_id)
        self.db.add(favorite)
        self.db.commit()
        return True
    
    def remove_from_favorites(self, user_id: uuid.UUID, product_id: uuid.UUID) -> bool:
        """Remove product from user favorites"""
        favorite = (
            self.db.query(UserFavorite)
            .filter(
                UserFavorite.user_id == user_id,
                UserFavorite.product_id == product_id
            )
            .first()
        )
        
        if not favorite:
            return False  # Not favorited
        
        self.db.delete(favorite)
        self.db.commit()
        return True
    
    def get_user_favorites(self, user_id: uuid.UUID) -> List[Product]:
        """Get user's favorite products"""
        return (
            self.db.query(Product)
            .join(UserFavorite)
            .filter(
                UserFavorite.user_id == user_id,
                Product.is_available == True
            )
            .order_by(Product.name)
            .all()
        )
    
    def get_products_by_category(self, category_id: uuid.UUID) -> List[Product]:
        """Get all products in a category"""
        return (
            self.db.query(Product)
            .filter(
                Product.category_id == category_id,
                Product.is_available == True
            )
            .order_by(Product.sort_order, Product.name)
            .all()
        )