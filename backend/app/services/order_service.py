"""
Order business logic service
"""
import uuid
from typing import Optional, List
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.models.order import Order
from app.models.order_item import OrderItem
from app.models.product import Product
from app.models.user import User
from app.models.order_status_history import OrderStatusHistory
from app.schemas.order import OrderCreate, OrderUpdate


class OrderService:
    """Service for order-related business logic"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_order(self, user_id: uuid.UUID, order_data: OrderCreate) -> Order:
        """Create a new order with items"""
        # Calculate totals
        subtotal = Decimal('0.00')
        
        # Validate products and calculate subtotal
        for item_data in order_data.order_items:
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"Product {item_data.product_id} not found")
            if not product.is_available:
                raise ValueError(f"Product {product.name} is not available")
            
            subtotal += item_data.total_price
        
        # Create order
        order_dict = order_data.model_dump(exclude={'order_items'})
        order_dict['user_id'] = user_id
        order_dict['subtotal'] = subtotal
        
        # Generate order number
        order_number = self._generate_order_number()
        order_dict['order_number'] = order_number
        
        order = Order(**order_dict)
        self.db.add(order)
        self.db.flush()  # Get order ID
        
        # Create order items
        for item_data in order_data.order_items:
            order_item = OrderItem(
                order_id=order.id,
                **item_data.model_dump()
            )
            self.db.add(order_item)
        
        # Create initial status history
        status_history = OrderStatusHistory(
            order_id=order.id,
            status="pending",
            notes="Order created"
        )
        self.db.add(status_history)
        
        self.db.commit()
        self.db.refresh(order)
        return order
    
    def update_order_status(
        self, 
        order: Order, 
        new_status: str, 
        notes: Optional[str] = None,
        changed_by: Optional[uuid.UUID] = None
    ) -> Order:
        """Update order status and create history record"""
        valid_statuses = [
            'pending', 'paid', 'confirmed', 'preparing', 
            'ready', 'out_for_delivery', 'delivered', 'cancelled'
        ]
        
        if new_status not in valid_statuses:
            raise ValueError(f"Invalid status: {new_status}")
        
        old_status = order.status
        order.status = new_status
        
        # Update timestamps based on status
        if new_status == 'confirmed':
            order.confirmed_at = datetime.utcnow()
            order.estimated_delivery_time = datetime.utcnow() + timedelta(minutes=45)
        elif new_status in ['delivered', 'cancelled']:
            order.completed_at = datetime.utcnow()
        
        # Create status history
        status_history = OrderStatusHistory(
            order_id=order.id,
            status=new_status,
            notes=notes or f"Status changed from {old_status} to {new_status}",
            changed_by=changed_by
        )
        self.db.add(status_history)
        
        self.db.commit()
        self.db.refresh(order)
        return order
    
    def cancel_order(
        self, 
        order: Order, 
        reason: Optional[str] = None,
        cancelled_by: Optional[uuid.UUID] = None
    ) -> Order:
        """Cancel an order"""
        if order.status in ['delivered', 'cancelled']:
            raise ValueError("Cannot cancel order with status: {order.status}")
        
        return self.update_order_status(
            order, 
            'cancelled', 
            notes=f"Order cancelled. Reason: {reason or 'No reason provided'}",
            changed_by=cancelled_by
        )
    
    def get_user_orders(
        self, 
        user_id: uuid.UUID, 
        skip: int = 0, 
        limit: int = 20
    ) -> List[Order]:
        """Get orders for a specific user"""
        return (
            self.db.query(Order)
            .filter(Order.user_id == user_id)
            .order_by(Order.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_orders_by_status(
        self, 
        status: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Order]:
        """Get orders by status"""
        return (
            self.db.query(Order)
            .filter(Order.status == status)
            .order_by(Order.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def calculate_order_total(self, order_items: List[OrderItem]) -> Decimal:
        """Calculate total order amount"""
        return sum(item.total_price for item in order_items)
    
    def _generate_order_number(self) -> str:
        """Generate unique order number"""
        from datetime import datetime
        today = datetime.now().strftime("%Y%m%d")
        
        # Get count of orders today
        today_orders = (
            self.db.query(Order)
            .filter(Order.order_number.like(f"ORD-{today}-%"))
            .count()
        )
        
        sequence = str(today_orders + 1).zfill(4)
        return f"ORD-{today}-{sequence}"