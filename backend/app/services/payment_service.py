"""
Payment business logic service
"""
import uuid
from typing import Optional, Dict, Any
from decimal import Decimal
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.payment import Payment
from app.models.order import Order
from app.schemas.payment import PaymentCreate, PaymentUpdate


class PaymentService:
    """Service for payment-related business logic"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_payment(self, payment_data: PaymentCreate) -> Payment:
        """Create a new payment record"""
        # Validate order exists
        order = self.db.query(Order).filter(Order.id == payment_data.order_id).first()
        if not order:
            raise ValueError("Order not found")
        
        # Check if payment already exists for this order
        existing_payment = (
            self.db.query(Payment)
            .filter(
                Payment.order_id == payment_data.order_id,
                Payment.status.in_(["pending", "approved"])
            )
            .first()
        )
        
        if existing_payment:
            raise ValueError("Payment already exists for this order")
        
        payment = Payment(**payment_data.model_dump())
        self.db.add(payment)
        self.db.commit()
        self.db.refresh(payment)
        return payment
    
    def update_payment_status(
        self,
        payment: Payment,
        status: str,
        mp_payment_id: Optional[str] = None,
        payment_method: Optional[str] = None,
        failure_reason: Optional[str] = None
    ) -> Payment:
        """Update payment status"""
        valid_statuses = [
            "pending", "approved", "authorized", "in_process",
            "in_mediation", "rejected", "cancelled", "refunded"
        ]
        
        if status not in valid_statuses:
            raise ValueError(f"Invalid payment status: {status}")
        
        payment.status = status
        
        if mp_payment_id:
            payment.mp_payment_id = mp_payment_id
        
        if payment_method:
            payment.payment_method = payment_method
        
        if failure_reason:
            payment.failure_reason = failure_reason
        
        if status in ["approved", "authorized"]:
            payment.processed_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(payment)
        
        # Update order status if payment approved
        if status == "approved":
            self._update_order_on_payment_success(payment.order_id)
        elif status in ["rejected", "cancelled"]:
            self._update_order_on_payment_failure(payment.order_id)
        
        return payment
    
    def get_payment_by_mp_id(self, mp_payment_id: str) -> Optional[Payment]:
        """Get payment by Mercado Pago payment ID"""
        return (
            self.db.query(Payment)
            .filter(Payment.mp_payment_id == mp_payment_id)
            .first()
        )
    
    def get_order_payments(self, order_id: uuid.UUID) -> list[Payment]:
        """Get all payments for an order"""
        return (
            self.db.query(Payment)
            .filter(Payment.order_id == order_id)
            .order_by(Payment.created_at.desc())
            .all()
        )
    
    def process_webhook(self, webhook_data: Dict[str, Any]) -> Optional[Payment]:
        """Process Mercado Pago webhook"""
        if webhook_data.get("type") != "payment":
            return None
        
        mp_payment_id = webhook_data.get("data", {}).get("id")
        if not mp_payment_id:
            raise ValueError("Invalid webhook data: missing payment ID")
        
        payment = self.get_payment_by_mp_id(str(mp_payment_id))
        if not payment:
            return None  # Payment not found, ignore webhook
        
        # TODO: Fetch payment details from Mercado Pago API
        # This would require implementing the Mercado Pago API client
        
        return payment
    
    def calculate_refund_amount(self, payment: Payment, refund_percentage: float = 100.0) -> Decimal:
        """Calculate refund amount"""
        if refund_percentage < 0 or refund_percentage > 100:
            raise ValueError("Refund percentage must be between 0 and 100")
        
        return payment.amount * (Decimal(str(refund_percentage)) / 100)
    
    def process_refund(
        self,
        payment: Payment,
        refund_amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> Payment:
        """Process payment refund"""
        if payment.status != "approved":
            raise ValueError("Can only refund approved payments")
        
        if refund_amount is None:
            refund_amount = payment.amount
        
        if refund_amount > payment.amount:
            raise ValueError("Refund amount cannot exceed payment amount")
        
        # TODO: Process actual refund through Mercado Pago API
        
        # Update payment status
        payment.status = "refunded"
        payment.failure_reason = f"Refunded: {reason or 'No reason provided'}"
        
        self.db.commit()
        self.db.refresh(payment)
        
        return payment
    
    def _update_order_on_payment_success(self, order_id: uuid.UUID):
        """Update order status when payment is successful"""
        order = self.db.query(Order).filter(Order.id == order_id).first()
        if order and order.status == "pending":
            order.status = "paid"
            self.db.commit()
    
    def _update_order_on_payment_failure(self, order_id: uuid.UUID):
        """Update order status when payment fails"""
        order = self.db.query(Order).filter(Order.id == order_id).first()
        if order and order.status in ["pending", "paid"]:
            order.status = "cancelled"
            self.db.commit()