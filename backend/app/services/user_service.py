"""
User business logic service
"""
from typing import Optional, List
from sqlalchemy.orm import Session
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class UserService:
    """Service for user-related business logic"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_clerk_id(self, clerk_id: str) -> Optional[User]:
        """Get user by Clerk ID"""
        return self.db.query(User).filter(User.clerk_id == clerk_id).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return self.db.query(User).filter(User.email == email).first()
    
    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user"""
        user = User(**user_data.model_dump())
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user
    
    def update_user(self, user: User, user_data: UserUpdate) -> User:
        """Update user data"""
        update_data = user_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        self.db.commit()
        self.db.refresh(user)
        return user
    
    def deactivate_user(self, user: User) -> User:
        """Deactivate user (soft delete)"""
        user.is_active = False
        self.db.commit()
        return user
    
    def get_users_by_role(self, role: str, skip: int = 0, limit: int = 100) -> List[User]:
        """Get users by role with pagination"""
        return (
            self.db.query(User)
            .filter(User.role == role, User.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def sync_user_with_clerk(self, clerk_id: str, clerk_data: dict) -> Optional[User]:
        """Sync user data with Clerk information"""
        user = self.get_user_by_clerk_id(clerk_id)
        if not user:
            return None
        
        # Update user data from Clerk
        if "email" in clerk_data:
            user.email = clerk_data["email"]
        if "given_name" in clerk_data:
            user.first_name = clerk_data["given_name"]
        if "family_name" in clerk_data:
            user.last_name = clerk_data["family_name"]
        
        self.db.commit()
        self.db.refresh(user)
        return user