"""
Mercado Pago integration service
"""
import uuid
from typing import Dict, Any, Optional
from decimal import Decimal
import mercadopago
from app.core.config import settings
from app.models.order import Order
from app.models.payment import Payment


class MercadoPagoService:
    """Service for Mercado Pago integration"""
    
    def __init__(self):
        self.sdk = mercadopago.SDK(settings.mercado_pago_access_token)
    
    def create_payment_preference(
        self,
        order: Order,
        success_url: str = "http://localhost:3000/payment/success",
        failure_url: str = "http://localhost:3000/payment/failure",
        pending_url: str = "http://localhost:3000/payment/pending"
    ) -> Dict[str, Any]:
        """Create payment preference for an order"""
        
        # Build items array
        items = []
        for item in order.order_items:
            items.append({
                "id": str(item.product_id),
                "title": item.product_name,
                "currency_id": "ARS",
                "picture_url": "",  # TODO: Add product image URL
                "description": item.special_requests or item.product_name,
                "category_id": "food",
                "quantity": item.quantity,
                "unit_price": float(item.unit_price)
            })
        
        # Payment preference data
        preference_data = {
            "items": items,
            "payer": {
                "name": order.user.first_name or "",
                "surname": order.user.last_name or "",
                "email": order.user.email,
                "phone": {
                    "area_code": "",
                    "number": order.user.phone or ""
                },
                "identification": {
                    "type": "DNI",
                    "number": ""
                },
                "address": self._get_payer_address(order)
            },
            "payment_methods": {
                "excluded_payment_methods": [],
                "excluded_payment_types": [],
                "installments": 12,
                "default_installments": 1
            },
            "shipments": {
                "mode": "not_specified",
                "local_pickup": order.order_type == "pickup",
                "dimensions": "30x30x30,500",  # Default dimensions
                "receiver_address": self._get_shipping_address(order)
            },
            "back_urls": {
                "success": success_url,
                "failure": failure_url,
                "pending": pending_url
            },
            "auto_return": "approved",
            "external_reference": order.order_number,
            "notification_url": "http://localhost:8000/api/v1/payments/webhook/mercadopago",
            "statement_descriptor": "SaaS Comidas Rapidas",
            "expires": True,
            "expiration_date_from": order.created_at.isoformat(),
            "expiration_date_to": self._get_expiration_date(order).isoformat()
        }
        
        # Create preference
        preference_response = self.sdk.preference().create(preference_data)
        
        if preference_response["status"] == 201:
            return {
                "preference_id": preference_response["response"]["id"],
                "init_point": preference_response["response"]["init_point"],
                "sandbox_init_point": preference_response["response"]["sandbox_init_point"],
                "collector_id": preference_response["response"]["collector_id"]
            }
        else:
            raise Exception(f"Error creating payment preference: {preference_response}")
    
    def get_payment_info(self, payment_id: str) -> Dict[str, Any]:
        """Get payment information from Mercado Pago"""
        payment_response = self.sdk.payment().get(payment_id)
        
        if payment_response["status"] == 200:
            return payment_response["response"]
        else:
            raise Exception(f"Error getting payment info: {payment_response}")
    
    def process_webhook_notification(self, notification_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process webhook notification from Mercado Pago"""
        try:
            if notification_data.get("type") == "payment":
                payment_id = notification_data.get("data", {}).get("id")
                if payment_id:
                    return self.get_payment_info(str(payment_id))
            
            return None
        except Exception as e:
            print(f"Error processing webhook: {e}")
            return None
    
    def create_refund(self, payment_id: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """Create a refund for a payment"""
        refund_data = {}
        if amount:
            refund_data["amount"] = amount
        
        refund_response = self.sdk.refund().create(payment_id, refund_data)
        
        if refund_response["status"] in [200, 201]:
            return refund_response["response"]
        else:
            raise Exception(f"Error creating refund: {refund_response}")
    
    def _get_payer_address(self, order: Order) -> Dict[str, Any]:
        """Get payer address from order"""
        if not order.delivery_address:
            return {}
        
        return {
            "street_name": order.delivery_address.street_address,
            "street_number": "",
            "zip_code": order.delivery_address.postal_code or ""
        }
    
    def _get_shipping_address(self, order: Order) -> Dict[str, Any]:
        """Get shipping address from order"""
        if not order.delivery_address:
            return {}
        
        return {
            "street_name": order.delivery_address.street_address,
            "street_number": "",
            "zip_code": order.delivery_address.postal_code or "",
            "city_name": order.delivery_address.city,
            "state_name": order.delivery_address.state or "",
            "country_name": order.delivery_address.country
        }
    
    def _get_expiration_date(self, order: Order):
        """Get payment expiration date (1 hour from creation)"""
        from datetime import timedelta
        return order.created_at + timedelta(hours=1)
    
    def validate_webhook_signature(self, raw_body: str, signature: str) -> bool:
        """Validate webhook signature (implement based on MP documentation)"""
        # TODO: Implement signature validation
        # This should validate the webhook signature against the secret
        return True