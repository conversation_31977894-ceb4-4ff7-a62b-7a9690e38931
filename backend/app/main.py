"""
FastAPI Backend - SaaS Comidas Rápidas
Main application entry point
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from app.core.config import settings

# Configuración de la aplicación
app = FastAPI(
    title="SaaS Comidas Rápidas API",
    description="API para gestión de pedidos de comidas rápidas",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configuración de CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.backend_cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Endpoint raíz de la API"""
    return JSONResponse({
        "message": "SaaS Comidas Rápidas API",
        "version": "1.0.0",
        "status": "running"
    })

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return JSONResponse({
        "status": "healthy",
        "service": "fastapi-backend"
    })

# Incluir routers de la API
from app.api.v1.router import api_router
app.include_router(api_router, prefix="/api/v1")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)