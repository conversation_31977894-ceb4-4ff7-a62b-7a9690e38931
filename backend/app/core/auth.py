"""
Authentication utilities for Clerk integration
"""
import jwt
import requests
from typing import Op<PERSON>, Dict, Any
from fastapi import HTT<PERSON>Ex<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.core.config import settings

security = HTTPBearer()


class ClerkAuth:
    """Clerk authentication handler"""
    
    def __init__(self):
        self.secret_key = settings.clerk_secret_key
        self.jwks_url = "https://api.clerk.dev/v1/jwks"
        self._jwks_cache = None
    
    def get_jwks(self) -> Dict[str, Any]:
        """Get JWKS from Clerk"""
        if not self._jwks_cache:
            try:
                response = requests.get(self.jwks_url, timeout=10)
                response.raise_for_status()
                self._jwks_cache = response.json()
            except requests.RequestException as e:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail=f"Failed to fetch JWKS: {str(e)}"
                )
        return self._jwks_cache
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token from Clerk"""
        try:
            # For now, we'll decode without verification for development
            # In production, you should verify with JWKS
            decoded_token = jwt.decode(
                token, 
                options={"verify_signature": False}  # Disable for development
            )
            return decoded_token
        except jwt.InvalidTokenError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token: {str(e)}",
                headers={"WWW-Authenticate": "Bearer"},
            )


clerk_auth = ClerkAuth()


async def get_current_user_clerk_id(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """Get current user's Clerk ID from JWT token"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token_data = clerk_auth.verify_token(credentials.credentials)
    clerk_id = token_data.get("sub")
    
    if not clerk_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: missing subject",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return clerk_id


async def get_current_user_data(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """Get current user data from JWT token"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return clerk_auth.verify_token(credentials.credentials)


def require_role(allowed_roles: list[str]):
    """Decorator to require specific roles"""
    def role_checker(user_data: Dict[str, Any] = Depends(get_current_user_data)):
        user_role = user_data.get("role", "customer")  # Default to customer
        if user_role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {allowed_roles}"
            )
        return user_data
    return role_checker


# Common role dependencies
require_admin = require_role(["admin"])
require_staff = require_role(["admin", "staff"])
require_customer = require_role(["customer", "admin", "staff"])