"""
Configuración de la aplicación FastAPI
"""
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Database
    database_url: str = os.getenv("DATABASE_URL", "postgresql://admin:secure_password@localhost:5432/fastfood_db")
    
    # API
    api_v1_str: str = "/api/v1"
    project_name: str = "SaaS Comidas Rápidas"
    
    # CORS
    backend_cors_origins: list = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
    ]
    
    # Security
    secret_key: str = os.getenv("SECRET_KEY", "change-this-secret-key-in-production")
    
    # Clerk
    clerk_secret_key: str = os.getenv("CLERK_SECRET_KEY", "")
    
    # Mercado Pago
    mercado_pago_access_token: str = os.getenv("MERCADO_PAGO_ACCESS_TOKEN", "")
    
    class Config:
        env_file = ".env"

settings = Settings()