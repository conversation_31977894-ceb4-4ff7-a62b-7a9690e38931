version: '3.8'

services:
  # =====================================================
  # BASE DE DATOS POSTGRESQL
  # =====================================================
  database:
    image: postgres:15-alpine
    container_name: fastfood_db
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-fastfood_db}
      POSTGRES_USER: ${POSTGRES_USER:-admin}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    networks:
      - fastfood_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-admin} -d ${POSTGRES_DB:-fastfood_db}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =====================================================
  # ADMINER - ADMINISTRADOR DE BASE DE DATOS
  # =====================================================
  adminer:
    image: adminer:4.8.1
    container_name: fastfood_adminer
    restart: always
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: database
      ADMINER_DESIGN: lucas
    networks:
      - fastfood_network
    depends_on:
      database:
        condition: service_healthy

  # =====================================================
  # BACKEND FASTAPI
  # =====================================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fastfood_backend
    restart: always
    ports:
      - "8000:8000"
    environment:
      # Base de datos
      DATABASE_URL: postgresql://${POSTGRES_USER:-admin}:${POSTGRES_PASSWORD:-secure_password}@database:5432/${POSTGRES_DB:-fastfood_db}
      
      # Clerk Authentication
      CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
      
      # Mercado Pago
      MERCADO_PAGO_ACCESS_TOKEN: ${MERCADO_PAGO_ACCESS_TOKEN}
      MERCADO_PAGO_PUBLIC_KEY: ${MERCADO_PAGO_PUBLIC_KEY}
      
      # CORS y URLs
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:3001}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost:3000}
      
      # Configuración de aplicación
      APP_ENV: ${APP_ENV:-development}
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      DEBUG: ${DEBUG:-true}
      
      # Logs
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - ./backend:/app
      - /app/__pycache__
    networks:
      - fastfood_network
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # FRONTEND NEXT.JS
  # =====================================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: fastfood_frontend
    restart: always
    ports:
      - "3000:3000"
    environment:
      # Clerk Authentication
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
      
      # API URLs
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      NEXT_PUBLIC_FRONTEND_URL: ${NEXT_PUBLIC_FRONTEND_URL:-http://localhost:3000}
      
      # Mercado Pago
      NEXT_PUBLIC_MERCADO_PAGO_PUBLIC_KEY: ${NEXT_PUBLIC_MERCADO_PAGO_PUBLIC_KEY}
      
      # Configuración de Next.js
      NODE_ENV: ${NODE_ENV:-development}
      NEXT_TELEMETRY_DISABLED: 1
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - fastfood_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # REDIS PARA CACHE (OPCIONAL)
  # =====================================================
  redis:
    image: redis:7-alpine
    container_name: fastfood_redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_secure_password}
    volumes:
      - redis_data:/data
    networks:
      - fastfood_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

# =====================================================
# VOLÚMENES PERSISTENTES
# =====================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# =====================================================
# RED PERSONALIZADA
# =====================================================
networks:
  fastfood_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16